# API接口文档

本文档详细介绍了字段级别数据血缘图组件的API接口、数据格式和集成方法。

## 目录

- [核心API](#核心api)
  - [数据转换API](#数据转换api)
  - [SQL解析API](#sql解析api)
  - [图谱操作API](#图谱操作api)
- [数据格式](#数据格式)
  - [血缘数据格式](#血缘数据格式)
  - [G6图数据格式](#g6图数据格式)
  - [配置数据格式](#配置数据格式)
- [后端接口](#后端接口)
  - [SQL解析接口](#sql解析接口)
  - [血缘查询接口](#血缘查询接口)
  - [元数据接口](#元数据接口)
- [错误处理](#错误处理)
- [类型定义](#类型定义)

## 核心API

### 数据转换API

#### transformToG6Data

将血缘数据转换为G6图数据格式。

```typescript
function transformToG6Data(
  lineageData: LineageData,
  options?: TransformOptions
): G6GraphData

interface TransformOptions {
  enableFieldLevel?: boolean      // 是否启用字段级别显示
  showDataTypes?: boolean         // 是否显示数据类型
  nodeSpacing?: number           // 节点间距
  edgeSpacing?: number           // 边间距
  layoutDirection?: 'LR' | 'TB' | 'RL' | 'BT'  // 布局方向
}
```

**使用示例：**

```typescript
import { transformToG6Data } from '@/utils/graphDataTransform'

const g6Data = transformToG6Data(lineageData, {
  enableFieldLevel: true,
  showDataTypes: true,
  layoutDirection: 'LR'
})
```

#### transformRawDataToLineageData

将原始数据转换为标准血缘数据格式。

```typescript
function transformRawDataToLineageData(
  rawData: any,
  format: 'array' | 'object' | 'sql'
): LineageData

// 使用示例
const lineageData = transformRawDataToLineageData(rawJsonData, 'array')
```

#### validateLineageData

验证血缘数据的完整性和正确性。

```typescript
function validateLineageData(data: any): ValidationResult

interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  statistics: DataStatistics
}

interface ValidationError {
  type: 'MISSING_FIELD' | 'INVALID_TYPE' | 'BROKEN_REFERENCE'
  message: string
  path: string
  severity: 'error' | 'warning'
}
```

### SQL解析API

#### parseSqlToLineageData

解析SQL语句并生成血缘数据。

```typescript
function parseSqlToLineageData(
  sql: string,
  databaseType: DatabaseType,
  options?: ParseOptions
): Promise<LineageData>

interface ParseOptions {
  includeFieldLevel?: boolean     // 包含字段级别血缘
  includeCompleteLineage?: boolean // 包含完整血缘链路
  maxDepth?: number              // 最大解析深度
  enableInference?: boolean       // 启用智能推断
}
```

**使用示例：**

```typescript
import { parseSqlToLineageData } from '@/utils/sqlParser'

const lineageData = await parseSqlToLineageData(
  'SELECT u.id, u.name FROM users u JOIN orders o ON u.id = o.user_id',
  DatabaseType.MYSQL,
  {
    includeFieldLevel: true,
    maxDepth: 5
  }
)
```

#### formatSql

格式化SQL语句。

```typescript
function formatSql(sql: string, options?: FormatOptions): string

interface FormatOptions {
  indentSize?: number            // 缩进大小
  keywordCase?: 'upper' | 'lower' // 关键字大小写
  lineBreaks?: boolean           // 是否添加换行
}
```

### 图谱操作API

#### 搜索和定位

```typescript
// 搜索节点和字段
function searchNodes(
  keyword: string,
  data: G6GraphData,
  options?: SearchOptions
): SearchResult[]

interface SearchOptions {
  searchType?: 'table' | 'field' | 'both'
  fuzzyMatch?: boolean
  caseSensitive?: boolean
  maxResults?: number
}

// 定位到指定节点
function locateToNode(
  graph: Graph,
  nodeId: string,
  options?: LocateOptions
): void

interface LocateOptions {
  animate?: boolean
  duration?: number
  highlight?: boolean
  zoom?: number
}
```

#### 路径追踪

```typescript
// 查找血缘路径
function findLineagePaths(
  data: G6GraphData,
  fieldId: string,
  direction: 'upstream' | 'downstream' | 'both',
  maxDepth?: number
): PathTraceResult[]

interface PathTraceResult {
  path: string[]
  depth: number
  confidence: number
  transformTypes: TransformType[]
}
```

#### 图谱导出

```typescript
// 导出为图片
function exportAsImage(
  graph: Graph,
  format: 'png' | 'jpeg',
  options?: ExportOptions
): Promise<string>

interface ExportOptions {
  width?: number
  height?: number
  quality?: number
  backgroundColor?: string
  padding?: number
}

// 导出为PDF
function exportAsPDF(
  graph: Graph,
  options?: PDFExportOptions
): Promise<Blob>
```

## 数据格式

### 血缘数据格式

```typescript
interface LineageData {
  tables: Record<string, TableInfo>
  nodes: LineageNode[]
  edges: LineageEdge[]
  metadata: LineageMetadata
}

interface TableInfo {
  name: string
  type: 'table' | 'view' | 'cte'
  description?: string
  schema?: string
  database?: string
  fields: FieldInfo[]
  metadata?: Record<string, any>
}

interface FieldInfo {
  id: string
  fieldName: string
  tableName: string
  type: 'field'
  dataType: DataTypeInfo
  description?: string
  isPrimaryKey?: boolean
  isForeignKey?: boolean
  isNullable?: boolean
  defaultValue?: any
  metadata?: Record<string, any>
}

interface DataTypeInfo {
  type: string
  length?: number
  precision?: number
  scale?: number
  isPrimaryKey?: boolean
  isForeignKey?: boolean
  isNullable?: boolean
  enumValues?: string[]
}

interface LineageEdge {
  id: string
  source: string
  target: string
  transformType: TransformType
  label?: string
  description?: string
  confidence: number
  metadata?: Record<string, any>
}

enum TransformType {
  DIRECT = 'DIRECT',           // 直接映射
  ALIAS = 'ALIAS',             // 别名转换
  CALCULATION = 'CALCULATION', // 计算转换
  AGGREGATION = 'AGGREGATION', // 聚合转换
  JOIN = 'JOIN',               // 连接转换
  UNION = 'UNION',             // 联合转换
  FILTER = 'FILTER'            // 过滤转换
}
```

### G6图数据格式

```typescript
interface G6GraphData {
  nodes: G6NodeData[]
  edges: G6EdgeData[]
}

interface G6NodeData {
  id: string
  type: string
  x?: number
  y?: number
  label?: string
  style?: Record<string, any>
  data: LineageNode
}

interface G6EdgeData {
  id: string
  source: string
  target: string
  type?: string
  label?: string
  style?: Record<string, any>
  data: LineageEdge
}
```

### 配置数据格式

```typescript
interface UserConfig {
  // 主题设置
  theme: 'light' | 'dark'
  
  // 布局设置
  layoutDirection: 'LR' | 'TB' | 'RL' | 'BT'
  showFieldTypes: boolean
  showTableComments: boolean
  showFieldDescriptions: boolean
  showDataTypes: boolean
  
  // 性能设置
  performanceMode: 'normal' | 'optimized' | 'extreme'
  enableVirtualRendering: boolean
  
  // 字段筛选设置
  fieldFilter: FieldFilterConfig
  
  // 图谱设置
  graphConfig: GraphConfig
  
  // 控制开关
  showFieldLevelLineage: boolean
  showCompleteLineage: boolean
  
  // 其他设置
  autoSave: boolean
  showMiniMap: boolean
  enableTooltips: boolean
}

interface FieldFilterConfig {
  enabled: boolean
  dataTypes: string[]
  attributes: string[]
  tables: string[]
  fieldNamePattern: string
}

interface GraphConfig {
  width: number
  height: number
  fitView: boolean
  fitViewPadding: number
  animate: boolean
  animateCfg: {
    duration: number
    easing: string
  }
}
```

## 后端接口

### SQL解析接口

#### POST /api/sql/parse

解析SQL语句并返回血缘数据。

**请求格式：**

```typescript
interface ParseSqlRequest {
  sql: string
  databaseType: DatabaseType
  options?: {
    includeFieldLevel?: boolean
    includeCompleteLineage?: boolean
    maxDepth?: number
  }
}
```

**响应格式：**

```typescript
interface ParseSqlResponse {
  success: boolean
  data?: LineageData
  message?: string
  parseTime?: number
  warnings?: string[]
}
```

**使用示例：**

```typescript
const response = await fetch('/api/sql/parse', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    sql: 'SELECT u.id, u.name FROM users u',
    databaseType: 'mysql',
    options: {
      includeFieldLevel: true
    }
  })
})

const result: ParseSqlResponse = await response.json()
```

### 血缘查询接口

#### GET /api/lineage/query

查询指定表或字段的血缘关系。

**请求参数：**

```typescript
interface LineageQueryParams {
  tableName?: string
  fieldName?: string
  direction?: 'upstream' | 'downstream' | 'both'
  maxDepth?: number
  includeMetadata?: boolean
}
```

**响应格式：**

```typescript
interface LineageQueryResponse {
  success: boolean
  data?: {
    lineageData: LineageData
    paths: PathTraceResult[]
    statistics: {
      nodeCount: number
      edgeCount: number
      maxDepth: number
    }
  }
  message?: string
}
```

### 元数据接口

#### GET /api/metadata/tables

获取数据库表元数据。

**响应格式：**

```typescript
interface TablesMetadataResponse {
  success: boolean
  data?: {
    tables: TableMetadata[]
    schemas: string[]
    databases: string[]
  }
}

interface TableMetadata {
  name: string
  schema: string
  database: string
  type: 'table' | 'view'
  description?: string
  rowCount?: number
  createdAt?: string
  updatedAt?: string
  fields: FieldMetadata[]
}

interface FieldMetadata {
  name: string
  dataType: string
  isNullable: boolean
  isPrimaryKey: boolean
  isForeignKey: boolean
  defaultValue?: any
  description?: string
}
```

## 错误处理

### 错误类型

```typescript
enum ErrorType {
  VALIDATION = 'VALIDATION',     // 数据验证错误
  PARSING = 'PARSING',           // SQL解析错误
  NETWORK = 'NETWORK',           // 网络请求错误
  RENDERING = 'RENDERING',       // 图谱渲染错误
  BUSINESS = 'BUSINESS',         // 业务逻辑错误
  SYSTEM = 'SYSTEM',             // 系统错误
  UNKNOWN = 'UNKNOWN'            // 未知错误
}
```

### 错误响应格式

```typescript
interface ErrorResponse {
  success: false
  error: {
    type: ErrorType
    code: string
    message: string
    details?: any
    timestamp: string
    requestId?: string
  }
}
```

### 错误处理示例

```typescript
import { handleError, ErrorType } from '@/utils/errorManager'

try {
  const result = await parseSqlToLineageData(sql, databaseType)
  // 处理成功结果
} catch (error) {
  handleError(error, ErrorType.PARSING, {
    showNotification: true,
    retryable: true,
    context: { sql, databaseType }
  })
}
```

## 类型定义

完整的TypeScript类型定义请参考：

- `src/types/lineage.ts` - 核心数据类型
- `src/utils/apiClient.ts` - API接口类型
- `src/utils/errorManager.ts` - 错误处理类型
- `src/utils/configManager.ts` - 配置管理类型

### 主要枚举类型

```typescript
enum DatabaseType {
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  ORACLE = 'oracle',
  SQLSERVER = 'sqlserver',
  SQLITE = 'sqlite',
  CLICKHOUSE = 'clickhouse',
  HIVE = 'hive',
  SPARK = 'spark'
}

enum TransformType {
  DIRECT = 'DIRECT',
  ALIAS = 'ALIAS',
  CALCULATION = 'CALCULATION',
  AGGREGATION = 'AGGREGATION',
  JOIN = 'JOIN',
  UNION = 'UNION',
  FILTER = 'FILTER'
}

enum PerformanceMode {
  NORMAL = 'normal',
  OPTIMIZED = 'optimized',
  EXTREME = 'extreme'
}
```

这个API文档提供了完整的接口说明和使用示例，帮助开发者快速集成和使用血缘图组件。
