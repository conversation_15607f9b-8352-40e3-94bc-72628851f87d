/**
 * 性能测试工具
 * 用于测试血缘图组件在不同数据量下的性能表现
 */

import { transformToG6Data, applyDagreLayout, type DagreLayoutOptions } from './graphDataTransform'
import { createSampleLineageData } from './sqlParser'
import type { LineageData, G6GraphData } from '@/types/lineage'

// 性能测试结果接口
export interface PerformanceTestResult {
  testName: string
  dataSize: {
    nodeCount: number
    edgeCount: number
    fieldCount: number
  }
  metrics: {
    renderTime: number
    layoutTime: number
    memoryUsage: number
    fps: number
    cpuUsage: number
  }
  success: boolean
  error?: string
}

// 性能基准
export interface PerformanceBenchmark {
  small: { maxRenderTime: number; maxMemory: number; minFps: number }
  medium: { maxRenderTime: number; maxMemory: number; minFps: number }
  large: { maxRenderTime: number; maxMemory: number; minFps: number }
}

// 默认性能基准
export const DEFAULT_BENCHMARKS: PerformanceBenchmark = {
  small: { maxRenderTime: 100, maxMemory: 10, minFps: 60 },
  medium: { maxRenderTime: 500, maxMemory: 50, minFps: 30 },
  large: { maxRenderTime: 2000, maxMemory: 200, minFps: 15 }
}

// 内存监控工具
export class MemoryMonitor {
  private initialMemory: number = 0
  private peakMemory: number = 0

  start() {
    this.initialMemory = this.getCurrentMemory()
    this.peakMemory = this.initialMemory
  }

  update() {
    const current = this.getCurrentMemory()
    if (current > this.peakMemory) {
      this.peakMemory = current
    }
  }

  getUsage() {
    return {
      initial: this.initialMemory,
      peak: this.peakMemory,
      current: this.getCurrentMemory(),
      increase: this.peakMemory - this.initialMemory
    }
  }

  private getCurrentMemory(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / (1024 * 1024) // MB
    }
    return 0
  }
}

// FPS监控工具
export class FPSMonitor {
  private frames: number[] = []
  private lastTime: number = 0
  private isRunning: boolean = false

  start() {
    this.frames = []
    this.lastTime = performance.now()
    this.isRunning = true
    this.tick()
  }

  stop() {
    this.isRunning = false
  }

  getFPS(): number {
    if (this.frames.length === 0) return 0
    const sum = this.frames.reduce((a, b) => a + b, 0)
    return Math.round(sum / this.frames.length)
  }

  private tick() {
    if (!this.isRunning) return

    const now = performance.now()
    const delta = now - this.lastTime
    const fps = 1000 / delta

    this.frames.push(fps)
    if (this.frames.length > 60) { // 保持最近60帧的数据
      this.frames.shift()
    }

    this.lastTime = now
    requestAnimationFrame(() => this.tick())
  }
}

// 性能测试器
export class PerformanceTester {
  private memoryMonitor = new MemoryMonitor()
  private fpsMonitor = new FPSMonitor()

  async runDataSizeTest(sizes: number[]): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = []

    for (const size of sizes) {
      try {
        const result = await this.testDataSize(size)
        results.push(result)
      } catch (error) {
        results.push({
          testName: `数据量测试 (${size}节点)`,
          dataSize: { nodeCount: size, edgeCount: 0, fieldCount: 0 },
          metrics: { renderTime: 0, layoutTime: 0, memoryUsage: 0, fps: 0, cpuUsage: 0 },
          success: false,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    return results
  }

  async testDataSize(nodeCount: number): Promise<PerformanceTestResult> {
    // 生成测试数据
    const testData = this.generateTestData(nodeCount)

    // 开始监控
    this.memoryMonitor.start()
    this.fpsMonitor.start()

    // 测试数据转换性能
    const transformStart = performance.now()
    const g6Data = transformToG6Data(testData)
    const transformTime = performance.now() - transformStart

    // 测试布局算法性能
    const layoutStart = performance.now()
    const layoutOptions: DagreLayoutOptions = {
      rankdir: 'LR',
      align: 'UL',
      nodesep: 80,
      ranksep: 150,
      enableOptimization: nodeCount > 100
    }
    const layoutedData = applyDagreLayout(g6Data, layoutOptions)
    const layoutTime = performance.now() - layoutStart

    // 等待一段时间以获取稳定的FPS数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 停止监控
    this.fpsMonitor.stop()
    const memoryUsage = this.memoryMonitor.getUsage()
    const fps = this.fpsMonitor.getFPS()

    const totalRenderTime = transformTime + layoutTime

    return {
      testName: `数据量测试 (${nodeCount}节点)`,
      dataSize: {
        nodeCount: testData.nodes.length,
        edgeCount: testData.edges.length,
        fieldCount: testData.nodes.length // 简化计算
      },
      metrics: {
        renderTime: Math.round(totalRenderTime),
        layoutTime: Math.round(layoutTime),
        memoryUsage: Math.round(memoryUsage.increase),
        fps: fps,
        cpuUsage: this.estimateCPUUsage(totalRenderTime)
      },
      success: true
    }
  }

  async runStressTest(duration: number = 10000): Promise<PerformanceTestResult> {
    const startTime = performance.now()
    const operations: number[] = []

    this.memoryMonitor.start()
    this.fpsMonitor.start()

    // 持续执行操作直到达到指定时间
    while (performance.now() - startTime < duration) {
      const opStart = performance.now()

      // 执行一系列操作
      const testData = this.generateTestData(50)
      const g6Data = transformToG6Data(testData)
      applyDagreLayout(g6Data, { rankdir: 'LR' })

      const opTime = performance.now() - opStart
      operations.push(opTime)

      this.memoryMonitor.update()

      // 短暂休息以避免阻塞
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    this.fpsMonitor.stop()
    const memoryUsage = this.memoryMonitor.getUsage()
    const fps = this.fpsMonitor.getFPS()

    const avgOperationTime = operations.reduce((a, b) => a + b, 0) / operations.length

    return {
      testName: `压力测试 (${duration}ms)`,
      dataSize: {
        nodeCount: operations.length * 50,
        edgeCount: operations.length * 30,
        fieldCount: operations.length * 50
      },
      metrics: {
        renderTime: Math.round(avgOperationTime),
        layoutTime: Math.round(avgOperationTime * 0.6), // 估算布局时间占比
        memoryUsage: Math.round(memoryUsage.increase),
        fps: fps,
        cpuUsage: this.estimateCPUUsage(avgOperationTime)
      },
      success: memoryUsage.increase < 100 && fps > 10 // 基本性能要求
    }
  }

  private generateTestData(nodeCount: number): LineageData {
    // 根据节点数量生成不同复杂度的测试数据
    if (nodeCount <= 50) {
      return createSampleLineageData('basic')
    } else if (nodeCount <= 200) {
      return createSampleLineageData('complex')
    } else {
      // 生成大数据量测试数据
      return this.generateLargeTestData(nodeCount)
    }
  }

  private generateLargeTestData(nodeCount: number): LineageData {
    const nodes = []
    const edges = []
    const tables: { [key: string]: any } = {}

    const tableCount = Math.ceil(nodeCount / 10) // 每个表平均10个字段

    for (let i = 0; i < tableCount; i++) {
      const tableName = `table_${i}`
      const fieldsPerTable = Math.floor(nodeCount / tableCount)

      tables[tableName] = {
        name: tableName,
        type: 'table',
        database: 'test_db',
        schema: 'public'
      }

      for (let j = 0; j < fieldsPerTable; j++) {
        const fieldId = `${tableName}.field_${j}`
        nodes.push({
          id: fieldId,
          type: 'field' as const,
          label: `field_${j}`,
          tableName: tableName,
          fieldName: `field_${j}`,
          dataType: { type: 'varchar', length: 255 },
          isPrimaryKey: j === 0,
          isNullable: j > 0,
          comment: `Test field ${j} in ${tableName}`
        })

        // 创建一些边连接
        if (i > 0 && j < 3) {
          const sourceTableIndex = Math.floor(Math.random() * i)
          const sourceFieldIndex = Math.floor(Math.random() * 3)
          const sourceId = `table_${sourceTableIndex}.field_${sourceFieldIndex}`

          edges.push({
            id: `edge_${edges.length}`,
            source: sourceId,
            target: fieldId,
            transformationType: 'direct',
            confidence: 0.9
          })
        }
      }
    }

    return { nodes, edges, tables }
  }

  private estimateCPUUsage(renderTime: number): number {
    // 基于渲染时间估算CPU使用率
    // 这是一个简化的估算方法
    const baseUsage = 10 // 基础使用率
    const timeBasedUsage = Math.min(renderTime / 10, 80) // 基于时间的使用率
    return Math.round(baseUsage + timeBasedUsage)
  }

  evaluatePerformance(result: PerformanceTestResult, benchmarks: PerformanceBenchmark = DEFAULT_BENCHMARKS): {
    category: 'small' | 'medium' | 'large'
    passed: boolean
    issues: string[]
  } {
    const { nodeCount } = result.dataSize
    const { renderTime, memoryUsage, fps } = result.metrics

    let category: 'small' | 'medium' | 'large'
    let benchmark

    if (nodeCount <= 50) {
      category = 'small'
      benchmark = benchmarks.small
    } else if (nodeCount <= 200) {
      category = 'medium'
      benchmark = benchmarks.medium
    } else {
      category = 'large'
      benchmark = benchmarks.large
    }

    const issues: string[] = []

    if (renderTime > benchmark.maxRenderTime) {
      issues.push(`渲染时间过长: ${renderTime}ms > ${benchmark.maxRenderTime}ms`)
    }

    if (memoryUsage > benchmark.maxMemory) {
      issues.push(`内存使用过多: ${memoryUsage}MB > ${benchmark.maxMemory}MB`)
    }

    if (fps < benchmark.minFps) {
      issues.push(`FPS过低: ${fps} < ${benchmark.minFps}`)
    }

    return {
      category,
      passed: issues.length === 0,
      issues
    }
  }
}

// 导出单例实例
export const performanceTester = new PerformanceTester()
