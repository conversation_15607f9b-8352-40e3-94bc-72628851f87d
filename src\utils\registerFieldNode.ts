/**
 * G6自定义字段节点和边注册
 * 用于注册卡片式表节点，支持字段列表显示和字段级连线
 */

import { Rect, register, ExtensionCategory, BaseEdge } from '@antv/g6'
import type { LineageNode, TableInfo, LineageEdge } from '@/types/lineage'
import { getFieldTypeColor, getTableNameFromFieldId, getFieldNameFromFieldId } from './graphDataTransform'

/**
 * 节点样式配置
 */
export interface NodeStyleConfig {
  // 节点基础样式
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  shadowColor: string;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;

  // 标题样式
  headerBackgroundColor: string;
  headerTextColor: string;
  headerFontSize: number;
  headerFontWeight: string;
  headerPadding: number;

  // 字段样式
  fieldTextColor: string;
  fieldFontSize: number;
  fieldLineHeight: number;
  fieldPadding: number;
  fieldHoverBackgroundColor: string;
  fieldActiveBackgroundColor: string;

  // 类型标签样式
  typeTagBackgroundColor: string;
  typeTagTextColor: string;
  typeTagFontSize: number;
  typeTagPadding: number;
  typeTagBorderRadius: number;
}

/**
 * 默认节点样式 - 现代扁平化卡片设计
 */
export const DEFAULT_NODE_STYLE: NodeStyleConfig = {
  backgroundColor: '#ffffff',
  borderColor: 'rgba(0, 0, 0, 0.06)',
  borderWidth: 1,
  borderRadius: 8,
  shadowColor: 'rgba(0, 0, 0, 0.08)',
  shadowBlur: 16,
  shadowOffsetX: 0,
  shadowOffsetY: 4,

  headerBackgroundColor: '#fafafa',
  headerTextColor: '#262626',
  headerFontSize: 14,
  headerFontWeight: '600',
  headerPadding: 16,

  fieldTextColor: '#595959',
  fieldFontSize: 12,
  fieldLineHeight: 24,
  fieldPadding: 12,
  fieldHoverBackgroundColor: '#f5f5f5',
  fieldActiveBackgroundColor: '#e6f7ff',

  typeTagBackgroundColor: '#f0f0f0',
  typeTagTextColor: '#8c8c8c',
  typeTagFontSize: 10,
  typeTagPadding: 4,
  typeTagBorderRadius: 4
}

/**
 * 自定义表节点类
 */
class TableNode extends Rect {
  // 获取节点数据
  get nodeData(): any {
    return this.context.graph.getNodeData(this.id).data || {}
  }

  // 获取表信息
  get tableInfo(): TableInfo {
    return this.nodeData.tableInfo || { name: '', type: 'table' as const, fields: [] }
  }

  // 获取字段列表
  get fields(): LineageNode[] {
    return this.nodeData.fields || []
  }

  // 计算节点尺寸
  getNodeSize(attributes: any): [number, number] {
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const width = 240 // 固定宽度
    const height = calculateNodeHeight(this.fields, style)
    return [width, height]
  }

  // 获取表头样式 - 现代卡片设计
  getHeaderStyle(attributes: any) {
    const [width] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }

    return {
      x: -width / 2,
      y: -this.getNodeSize(attributes)[1] / 2,
      width: width,
      height: style.headerFontSize + style.headerPadding * 2,
      fill: style.headerBackgroundColor,
      stroke: style.borderColor,
      lineWidth: style.borderWidth,
      radius: [style.borderRadius, style.borderRadius, 0, 0],
      // 现代化阴影效果
      shadowColor: style.shadowColor,
      shadowBlur: style.shadowBlur,
      shadowOffsetX: style.shadowOffsetX,
      shadowOffsetY: style.shadowOffsetY
    }
  }

  // 绘制表头
  drawHeaderShape(attributes: any, container: any) {
    const headerStyle = this.getHeaderStyle(attributes)
    this.upsert('header', 'rect', headerStyle, container)
  }

  // 获取表名文本样式
  getTableNameStyle(attributes: any) {
    const [width] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }

    return {
      x: 0,
      y: -this.getNodeSize(attributes)[1] / 2 + style.headerPadding + style.headerFontSize / 2,
      text: this.tableInfo.name,
      fontSize: style.headerFontSize,
      fontWeight: style.headerFontWeight,
      fill: style.headerTextColor,
      textAlign: 'center',
      textBaseline: 'middle'
    }
  }

  // 绘制表名
  drawTableNameShape(attributes: any, container: any) {
    const tableNameStyle = this.getTableNameStyle(attributes)
    this.upsert('tableName', 'text', tableNameStyle, container)
  }

  // 获取字段容器样式 - 现代卡片设计
  getFieldsContainerStyle(attributes: any) {
    const [width, height] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const headerHeight = style.headerFontSize + style.headerPadding * 2

    return {
      x: -width / 2,
      y: -height / 2 + headerHeight,
      width: width,
      height: height - headerHeight,
      fill: style.backgroundColor,
      stroke: style.borderColor,
      lineWidth: style.borderWidth,
      radius: [0, 0, style.borderRadius, style.borderRadius],
      // 现代化阴影效果
      shadowColor: style.shadowColor,
      shadowBlur: style.shadowBlur,
      shadowOffsetX: style.shadowOffsetX,
      shadowOffsetY: style.shadowOffsetY
    }
  }

  // 绘制字段容器
  drawFieldsContainerShape(attributes: any, container: any) {
    const fieldsContainerStyle = this.getFieldsContainerStyle(attributes)
    this.upsert('fieldsContainer', 'rect', fieldsContainerStyle, container)
  }

  // 绘制字段列表
  drawFieldsShape(attributes: any, container: any) {
    const [width, height] = this.getNodeSize(attributes)
    const style = { ...DEFAULT_NODE_STYLE, ...attributes }
    const headerHeight = style.headerFontSize + style.headerPadding * 2

    this.fields.forEach((field, index) => {
      const fieldY = -height / 2 + headerHeight + style.fieldPadding + (index + 0.5) * style.fieldLineHeight

      // 字段交互区域背景（用于悬浮效果）- 现代化设计
      const fieldBgStyle = {
        x: -width / 2 + 4,
        y: fieldY - style.fieldLineHeight / 2 + 2,
        width: width - 8,
        height: style.fieldLineHeight - 4,
        fill: 'transparent',
        stroke: 'transparent',
        radius: 4,
        cursor: 'pointer',
        // 添加字段ID作为数据属性，用于事件识别
        fieldId: field.id,
        fieldIndex: index,
        // 悬浮状态样式
        states: {
          hover: {
            fill: style.fieldHoverBackgroundColor,
            stroke: 'rgba(24, 144, 255, 0.2)',
            lineWidth: 1
          },
          active: {
            fill: style.fieldActiveBackgroundColor,
            stroke: '#1890ff',
            lineWidth: 2
          }
        }
      }
      this.upsert(`fieldBg-${index}`, 'rect', fieldBgStyle, container)

      // 字段名
      const fieldNameStyle = {
        x: -width / 2 + 8,
        y: fieldY,
        text: field.fieldName,
        fontSize: style.fieldFontSize,
        fill: style.fieldTextColor,
        textAlign: 'left',
        textBaseline: 'middle',
        fontWeight: field.isKey ? 'bold' : 'normal',
        cursor: 'pointer'
      }
      this.upsert(`fieldName-${index}`, 'text', fieldNameStyle, container)

      // 数据类型
      if (field.dataType) {
        const typeText = formatDataType(field.dataType)
        const typeColor = getFieldTypeColor(field.dataType.type)

        const fieldTypeStyle = {
          x: width / 2 - 8,
          y: fieldY,
          text: typeText,
          fontSize: style.typeTagFontSize,
          fill: typeColor,
          textAlign: 'right',
          textBaseline: 'middle',
          cursor: 'pointer'
        }
        this.upsert(`fieldType-${index}`, 'text', fieldTypeStyle, container)
      }

      // 主键标识
      if (field.isKey) {
        const keyIconStyle = {
          x: -width / 2 + field.fieldName.length * style.fieldFontSize * 0.6 + 12,
          y: fieldY,
          text: '🔑',
          fontSize: style.fieldFontSize,
          textAlign: 'left',
          textBaseline: 'middle',
          cursor: 'pointer'
        }
        this.upsert(`keyIcon-${index}`, 'text', keyIconStyle, container)
      }
    })
  }

  // 主渲染方法
  render(attributes: any, container: any) {
    // 设置节点尺寸
    const [width, height] = this.getNodeSize(attributes)
    attributes.size = [width, height]

    // 不调用父类的render，因为我们要完全自定义
    // super.render(attributes, container)

    // 绘制表头
    this.drawHeaderShape(attributes, container)

    // 绘制表名
    this.drawTableNameShape(attributes, container)

    // 绘制字段容器
    this.drawFieldsContainerShape(attributes, container)

    // 绘制字段列表
    this.drawFieldsShape(attributes, container)
  }
}

/**
 * 注册表节点类型
 * @param styleCfg 样式配置
 */
export function registerTableNode(styleCfg: Partial<NodeStyleConfig> = {}) {
  const style = { ...DEFAULT_NODE_STYLE, ...styleCfg }

  // 注册自定义节点
  register(ExtensionCategory.NODE, 'table-node', TableNode)

  console.log('Table node registered successfully with style:', style)

  return TableNode
}

/**
 * 计算节点高度
 * @param fields 字段列表
 * @param style 样式配置
 * @returns 节点高度
 */
export function calculateNodeHeight(fields: LineageNode[], style: NodeStyleConfig): number {
  const headerHeight = style.headerFontSize + style.headerPadding * 2
  const fieldsHeight = fields.length * style.fieldLineHeight + style.fieldPadding * 2
  return headerHeight + fieldsHeight + 20 // 额外的底部间距
}

/**
 * 字段级边样式配置接口
 */
export interface FieldEdgeStyleConfig {
  strokeColor: string;
  strokeWidth: number;
  strokeDasharray?: string;
  arrowColor: string;
  arrowSize: number;
  labelFontSize: number;
  labelColor: string;
  labelBackgroundColor: string;
  labelPadding: number;
  animationDuration: number;
  curveOffset: number;
}

/**
 * 默认字段级边样式配置
 */
export const DEFAULT_FIELD_EDGE_STYLE: FieldEdgeStyleConfig = {
  strokeColor: '#1890ff',
  strokeWidth: 2,
  strokeDasharray: '',
  arrowColor: '#1890ff',
  arrowSize: 8,
  labelFontSize: 10,
  labelColor: '#666',
  labelBackgroundColor: '#ffffff',
  labelPadding: 4,
  animationDuration: 1000,
  curveOffset: 50
}

/**
 * 自定义字段级边类
 */
export class FieldEdge extends BaseEdge {
  private edgeStyle: FieldEdgeStyleConfig

  constructor(options: any) {
    super(options)
    this.edgeStyle = { ...DEFAULT_FIELD_EDGE_STYLE }
  }

  /**
   * 获取边的关键路径
   * @param attributes 边属性
   * @returns 路径数组
   */
  getKeyPath(attributes: any): any {
    // 使用G6内置的cubic-horizontal边类型的路径计算
    // 这里返回一个简单的直线路径作为默认实现
    const { sourcePoint, targetPoint } = attributes
    if (sourcePoint && targetPoint) {
      return [
        ['M', sourcePoint.x, sourcePoint.y],
        ['L', targetPoint.x, targetPoint.y]
      ]
    }
    return [
      ['M', 0, 0],
      ['L', 100, 0]
    ]
  }

  /**
   * 计算字段在节点中的位置
   * @param nodeData 节点数据
   * @param fieldId 字段ID
   * @returns 字段位置坐标 [x, y]
   */
  private getFieldPosition(nodeData: any, fieldId: string): [number, number] {
    const tableName = getTableNameFromFieldId(fieldId)
    const fieldName = getFieldNameFromFieldId(fieldId)

    if (nodeData.tableName !== tableName) {
      console.warn(`Field ${fieldId} does not belong to table ${nodeData.tableName}`)
      return [0, 0]
    }

    const fields = nodeData.fields || []
    const fieldIndex = fields.findIndex((field: LineageNode) => field.fieldName === fieldName)

    if (fieldIndex === -1) {
      console.warn(`Field ${fieldName} not found in table ${tableName}`)
      return [0, 0]
    }

    // 计算字段在节点中的相对位置
    const style = { ...DEFAULT_NODE_STYLE }
    const headerHeight = style.headerFontSize + style.headerPadding * 2
    const fieldY = headerHeight + style.fieldPadding + (fieldIndex + 0.5) * style.fieldLineHeight

    // 返回相对于节点中心的坐标
    const nodeHeight = calculateNodeHeight(fields, style)
    return [0, fieldY - nodeHeight / 2]
  }

  /**
   * 获取边的关键点坐标
   * @param attributes 边属性
   * @returns 关键点坐标
   */
  private getEdgeKeyPoints(attributes: any) {
    const { data } = attributes
    const lineageEdge = data?.lineageEdge

    if (!lineageEdge) {
      return null
    }

    // 这里简化处理，使用表级连接
    // 在实际的G6 v5中，节点位置信息会通过不同的方式获取
    return {
      sourceFieldId: lineageEdge.source,
      targetFieldId: lineageEdge.target,
      sourceField: getFieldNameFromFieldId(lineageEdge.source),
      targetField: getFieldNameFromFieldId(lineageEdge.target)
    }
  }

  /**
   * 渲染边
   * @param attributes 边属性
   * @param container 容器
   */
  render(attributes: any, container: any) {
    // 调用父类的渲染方法，使用G6内置的边渲染逻辑
    super.render(attributes, container)

    // 获取边的关键信息
    const keyPoints = this.getEdgeKeyPoints(attributes)

    if (keyPoints) {
      // 可以在这里添加额外的字段级连接视觉效果
      // 比如在连接点添加小圆点标识字段连接位置
      console.log('Field connection:', keyPoints)
    }
  }
}

/**
 * 注册字段级边类型
 * @param styleCfg 样式配置
 */
export function registerFieldEdge(styleCfg: Partial<FieldEdgeStyleConfig> = {}) {
  const style = { ...DEFAULT_FIELD_EDGE_STYLE, ...styleCfg }

  // 注册自定义边
  register(ExtensionCategory.EDGE, 'field-edge', FieldEdge)

  console.log('Field edge registered successfully with style:', style)

  return FieldEdge
}

/**
 * 注册所有自定义图形元素
 * @param nodeStyleCfg 节点样式配置
 * @param edgeStyleCfg 边样式配置
 */
export function registerAllGraphElements(
  nodeStyleCfg: Partial<NodeStyleConfig> = {},
  edgeStyleCfg: Partial<FieldEdgeStyleConfig> = {}
) {
  // 注册表节点
  registerTableNode(nodeStyleCfg)

  // 注册字段级边
  registerFieldEdge(edgeStyleCfg)

  console.log('All graph elements registered successfully')
}

/**
 * 格式化数据类型显示
 * @param dataType 数据类型对象
 * @returns 格式化后的类型字符串
 */
export function formatDataType(dataType: any): string {
  if (!dataType || !dataType.type) return ''

  let result = dataType.type

  if (dataType.length) {
    result += `(${dataType.length})`
  } else if (dataType.precision && dataType.scale) {
    result += `(${dataType.precision},${dataType.scale})`
  } else if (dataType.precision) {
    result += `(${dataType.precision})`
  }

  return result
}
