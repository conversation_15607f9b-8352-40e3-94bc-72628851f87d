# 开发指南

本文档为开发者提供了字段级别数据血缘图组件的开发指南，包括环境搭建、开发流程、最佳实践等。

## 目录

- [环境搭建](#环境搭建)
- [项目结构](#项目结构)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [性能优化](#性能优化)
- [部署指南](#部署指南)
- [常见问题](#常见问题)

## 环境搭建

### 系统要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **Git**: >= 2.20.0

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd lineage-web

# 安装依赖
npm install
# 或
yarn install
```

### 开发环境启动

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev

# 访问 http://localhost:5173
```

### 构建项目

```bash
# 类型检查
npm run type-check

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 项目结构

```
lineage-web/
├── docs/                    # 文档目录
│   ├── api.md              # API接口文档
│   ├── components.md       # 组件使用文档
│   ├── examples.md         # 示例数据和演示
│   └── development-guide.md # 开发指南
├── src/                     # 源代码目录
│   ├── components/         # Vue组件
│   │   ├── LineageLayout.vue    # 主布局组件
│   │   ├── LineageGraph.vue     # 图谱组件
│   │   ├── SqlEditor.vue        # SQL编辑器
│   │   ├── ErrorBoundary.vue    # 错误边界组件
│   │   ├── LoadingState.vue     # 加载状态组件
│   │   ├── FieldTooltip.vue     # 字段提示组件
│   │   ├── FieldDetailDrawer.vue # 字段详情抽屉
│   │   └── ConfigPanel.vue      # 配置面板
│   ├── stores/             # 状态管理
│   │   └── lineageStore.ts      # 血缘图状态管理
│   ├── types/              # 类型定义
│   │   └── lineage.ts           # 核心类型定义
│   ├── utils/              # 工具函数
│   │   ├── graphDataTransform.ts # 数据转换工具
│   │   ├── sqlParser.ts         # SQL解析工具
│   │   ├── registerFieldNode.ts # G6节点注册
│   │   ├── apiClient.ts         # API客户端
│   │   ├── configManager.ts     # 配置管理
│   │   └── errorManager.ts      # 错误管理
│   ├── tests/              # 测试页面
│   │   ├── FieldEdgeTest.vue         # 字段连线测试
│   │   ├── InteractionTest.vue       # 交互功能测试
│   │   ├── FieldInteractionTest.vue  # 字段交互测试
│   │   ├── AdvancedInteractionTest.vue # 高级交互测试
│   │   ├── DataTransformTest.vue     # 数据转换测试
│   │   ├── LayoutOptimizationTest.vue # 布局优化测试
│   │   ├── StyleOptimizationTest.vue # 样式优化测试
│   │   ├── PerformanceOptimizationTest.vue # 性能优化测试
│   │   ├── FunctionExtensionTest.vue # 功能扩展测试
│   │   └── ErrorHandlingTest.vue     # 错误处理测试
│   ├── views/              # 页面组件
│   │   ├── HomeView.vue         # 主页面
│   │   └── AboutView.vue        # 关于页面
│   ├── router/             # 路由配置
│   │   └── index.ts             # 路由定义
│   ├── assets/             # 静态资源
│   │   └── base.css             # 基础样式
│   ├── App.vue             # 应用根组件
│   └── main.ts             # 应用入口
├── public/                 # 公共资源
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
└── README.md               # 项目说明
```

## 开发流程

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# - 编写代码
# - 添加类型定义
# - 编写测试

# 3. 测试功能
npm run dev
# 访问对应的测试页面验证功能

# 4. 类型检查
npm run type-check

# 5. 构建测试
npm run build

# 6. 提交代码
git add .
git commit -m "feat: add new feature"

# 7. 推送分支
git push origin feature/new-feature
```

### 2. 组件开发规范

#### 创建新组件

```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props定义
interface Props {
  // 属性定义
}

const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

// Emits定义
interface Emits {
  (e: 'event-name', value: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const data = ref()

// 计算属性
const computed = computed(() => {
  // 计算逻辑
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法
const handleMethod = () => {
  // 方法实现
}

// 暴露给父组件的方法
defineExpose({
  handleMethod
})
</script>

<style scoped>
.component-name {
  /* 样式定义 */
}
</style>
```

#### 工具函数开发

```typescript
/**
 * 函数描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @returns 返回值描述
 */
export function utilityFunction(
  param1: Type1,
  param2: Type2
): ReturnType {
  try {
    // 函数实现
    return result
  } catch (error) {
    console.error('Function error:', error)
    throw error
  }
}
```

### 3. 测试页面开发

每个新功能都应该创建对应的测试页面：

```vue
<template>
  <div class="test-page">
    <h1>功能测试页面</h1>
    
    <!-- 测试控制区域 -->
    <div class="test-controls">
      <a-button @click="runTest">运行测试</a-button>
      <a-button @click="clearResults">清除结果</a-button>
    </div>
    
    <!-- 测试结果显示 -->
    <div class="test-results">
      <div v-for="result in testResults" :key="result.id">
        <!-- 测试结果项 -->
      </div>
    </div>
    
    <!-- 功能演示区域 -->
    <div class="demo-area">
      <!-- 功能演示组件 -->
    </div>
  </div>
</template>

<script setup lang="ts">
// 测试页面实现
</script>
```

## 代码规范

### 1. TypeScript规范

- 所有函数和变量都必须有明确的类型定义
- 使用接口定义复杂对象类型
- 避免使用 `any` 类型，使用 `unknown` 替代
- 使用枚举定义常量集合

```typescript
// 好的示例
interface UserData {
  id: number
  name: string
  email: string
}

function processUser(user: UserData): string {
  return `${user.name} (${user.email})`
}

// 避免的示例
function processUser(user: any): any {
  return user.name + ' (' + user.email + ')'
}
```

### 2. Vue组件规范

- 使用 Composition API
- Props和Emits必须有类型定义
- 使用 `defineExpose` 暴露组件方法
- 样式使用 `scoped`

### 3. 命名规范

- **文件名**: PascalCase (如: `LineageGraph.vue`)
- **组件名**: PascalCase (如: `LineageGraph`)
- **函数名**: camelCase (如: `transformData`)
- **变量名**: camelCase (如: `userData`)
- **常量名**: UPPER_SNAKE_CASE (如: `MAX_RETRY_COUNT`)
- **类型名**: PascalCase (如: `LineageData`)

### 4. 注释规范

```typescript
/**
 * 函数功能描述
 * 
 * @param param1 - 参数1描述
 * @param param2 - 参数2描述
 * @returns 返回值描述
 * 
 * @example
 * ```typescript
 * const result = functionName(value1, value2)
 * ```
 */
function functionName(param1: Type1, param2: Type2): ReturnType {
  // 实现逻辑
}
```

## 测试指南

### 1. 功能测试

每个功能都有对应的测试页面，通过以下步骤进行测试：

```bash
# 启动开发服务器
npm run dev

# 访问测试页面
# http://localhost:5173/test - 基础功能测试
# http://localhost:5173/interaction-test - 交互功能测试
# http://localhost:5173/field-interaction-test - 字段交互测试
# http://localhost:5173/advanced-interaction-test - 高级交互测试
# http://localhost:5173/data-transform-test - 数据转换测试
# http://localhost:5173/test/layout-optimization - 布局优化测试
# http://localhost:5173/style-test - 样式优化测试
# http://localhost:5173/performance-test - 性能优化测试
# http://localhost:5173/function-extension-test - 功能扩展测试
# http://localhost:5173/error-handling-test - 错误处理测试
```

### 2. 类型检查

```bash
# 运行TypeScript类型检查
npm run type-check

# 确保没有类型错误
```

### 3. 构建测试

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 4. 手动测试清单

- [ ] 组件正常渲染
- [ ] 交互功能正常
- [ ] 错误处理正确
- [ ] 性能表现良好
- [ ] 响应式布局正常
- [ ] 主题切换正常
- [ ] 数据转换正确
- [ ] API调用正常

## 性能优化

### 1. 组件性能优化

- 使用 `v-memo` 缓存复杂计算
- 使用 `shallowRef` 和 `shallowReactive` 优化大对象
- 合理使用 `computed` 缓存计算结果
- 避免在模板中使用复杂表达式

### 2. 图谱性能优化

- 大数据量时启用虚拟渲染
- 使用节流优化高频交互
- 合理设置性能模式
- 优化节点和边的样式复杂度

### 3. 内存管理

- 及时清理事件监听器
- 避免内存泄漏
- 合理使用对象缓存
- 定期清理不需要的数据

## 部署指南

### 1. 构建生产版本

```bash
# 安装依赖
npm install

# 类型检查
npm run type-check

# 构建项目
npm run build

# 构建产物在 dist/ 目录
```

### 2. 静态部署

构建后的 `dist/` 目录可以部署到任何静态文件服务器：

- Nginx
- Apache
- CDN
- 云存储服务

### 3. 环境配置

根据部署环境配置相应的环境变量和API地址。

## 常见问题

### 1. 开发环境问题

**Q: 启动开发服务器失败**
A: 检查Node.js版本是否 >= 18.0.0，清除node_modules重新安装依赖

**Q: 类型检查失败**
A: 检查TypeScript配置，确保所有类型定义正确

### 2. 功能问题

**Q: 图谱不显示**
A: 检查数据格式是否正确，查看控制台错误信息

**Q: 性能问题**
A: 启用性能优化模式，减少数据量或使用虚拟渲染

### 3. 构建问题

**Q: 构建失败**
A: 检查代码语法错误，确保所有依赖正确安装

**Q: 构建产物过大**
A: 检查是否有未使用的依赖，考虑代码分割优化

这个开发指南为开发者提供了完整的开发流程和最佳实践，帮助快速上手和高效开发。
