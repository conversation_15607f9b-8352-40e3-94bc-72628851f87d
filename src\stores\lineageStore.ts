/**
 * 血缘图数据状态管理
 * 使用 Pinia 管理图谱数据状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  LineageData,
  G6GraphData,
  LineageNode,
  LineageEdge,
  TableInfo,
  SearchResult,
  PathTraceResult,
  ThemeConfig
} from '@/types/lineage'
import { DatabaseType } from '@/types/lineage'
import {
  transformToG6Data,
  validateLineageData,
  sanitizeLineageData
} from '@/utils/graphDataTransform'
import { createSampleLineageData } from '@/utils/sqlParser'

export const useLineageStore = defineStore('lineage', () => {
  // ===== 状态定义 =====

  // 原始血缘数据
  const lineageData = ref<LineageData | null>(null)

  // G6图数据
  const g6GraphData = ref<G6GraphData | null>(null)

  // 当前SQL文本
  const sqlText = ref<string>('')

  // 数据库类型
  const databaseType = ref<DatabaseType>(DatabaseType.MYSQL)

  // 加载状态管理
  const loading = ref<boolean>(false)
  const loadingText = ref<string>('加载中...')
  const loadingProgress = ref<number | undefined>(undefined)
  const loadingSteps = ref<Array<{
    text: string
    status: 'pending' | 'active' | 'completed' | 'error'
  }>>([])

  // 错误状态管理
  const error = ref<string | null>(null)
  const errorDetails = ref<any>(null)
  const errorType = ref<string>('unknown')
  const retryCount = ref<number>(0)
  const maxRetries = ref<number>(3)

  // 图谱配置
  const graphConfig = ref({
    width: 800,
    height: 600,
    fitView: true,
    fitViewPadding: 20,
    animate: true,
    animateCfg: {
      duration: 500,
      easing: 'easeInOutCubic'
    }
  })

  // 控制开关状态
  const showFieldLevelLineage = ref<boolean>(true)
  const showCompleteLineage = ref<boolean>(true)

  // 选中的节点和边
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])

  // 悬浮的节点
  const hoveredNode = ref<string | null>(null)

  // 搜索关键词
  const searchKeyword = ref<string>('')

  // 字段筛选配置
  const fieldFilterConfig = ref({
    enabled: false,
    dataTypes: [] as string[],
    attributes: [] as string[],
    tables: [] as string[],
    fieldNamePattern: ''
  })

  // 主题配置 - 现代扁平化设计
  const theme = ref<ThemeConfig>({
    mode: 'light',
    colors: {
      primary: '#1890ff',
      secondary: '#52c41a',
      background: '#ffffff',
      surface: '#fafafa',
      text: '#262626',
      textSecondary: '#595959',
      border: 'rgba(0, 0, 0, 0.06)',
      shadow: 'rgba(0, 0, 0, 0.08)',
      // 节点样式 - 现代卡片设计
      nodeBackground: '#ffffff',
      nodeHeaderBackground: '#fafafa',
      nodeBorder: 'rgba(0, 0, 0, 0.06)',
      nodeText: '#262626',
      nodeFieldText: '#595959',
      nodeShadow: 'rgba(0, 0, 0, 0.08)',
      // 边样式 - 更柔和的颜色
      edgeStroke: '#d9d9d9',
      edgeActiveStroke: '#1890ff',
      edgeLabel: '#8c8c8c',
      // 交互状态 - 更明显的反馈
      hover: '#f5f5f5',
      active: '#e6f7ff',
      selected: '#1890ff',
      // 字段状态颜色
      fieldHover: '#f0f0f0',
      fieldActive: '#e6f7ff',
      fieldSelected: '#bae7ff'
    }
  })

  // ===== 计算属性 =====

  // 所有表信息
  const tables = computed(() => {
    return lineageData.value?.tables || {}
  })

  // 所有节点
  const nodes = computed(() => {
    return lineageData.value?.nodes || []
  })

  // 所有边
  const edges = computed(() => {
    return lineageData.value?.edges || []
  })

  // 表数量
  const tableCount = computed(() => {
    return Object.keys(tables.value).length
  })

  // 字段数量
  const fieldCount = computed(() => {
    return nodes.value.length
  })

  // 血缘关系数量
  const relationCount = computed(() => {
    return edges.value.length
  })

  // 搜索结果
  const searchResults = computed((): SearchResult[] => {
    if (!searchKeyword.value.trim()) return []

    const keyword = searchKeyword.value.toLowerCase()
    const results: SearchResult[] = []

    // 搜索表名
    Object.values(tables.value).forEach(table => {
      if (table.name.toLowerCase().includes(keyword)) {
        results.push({
          type: 'table',
          id: table.name,
          name: table.name,
          description: table.description,
          matchScore: table.name.toLowerCase().indexOf(keyword) === 0 ? 1 : 0.8
        })
      }
    })

    // 搜索字段名
    nodes.value.forEach(node => {
      if (node.fieldName.toLowerCase().includes(keyword)) {
        results.push({
          type: 'field',
          id: node.id,
          name: node.fieldName,
          tableName: node.tableName,
          description: node.description,
          matchScore: node.fieldName.toLowerCase().indexOf(keyword) === 0 ? 1 : 0.8
        })
      }
    })

    // 按匹配度排序
    return results.sort((a, b) => b.matchScore - a.matchScore)
  })

  // ===== 方法定义 =====

  /**
   * 设置血缘数据（增强版）
   * @param data 血缘数据
   */
  const setLineageData = async (data: LineageData) => {
    try {
      // 验证数据
      const validation = validateLineageData(data)

      if (!validation.isValid) {
        console.warn('血缘数据验证失败:', validation.errors)
        // 尝试清理数据
        const sanitized = sanitizeLineageData(data)
        if (sanitized.data) {
          lineageData.value = sanitized.data

          // 异步转换G6数据，避免阻塞UI
          await new Promise<void>((resolve) => {
            setTimeout(() => {
              try {
                g6GraphData.value = transformToG6Data(sanitized.data!)
                resolve()
              } catch (error) {
                console.error('G6数据转换失败:', error)
                resolve()
              }
            }, 10)
          })

          // 显示清理报告
          if (sanitized.report.fixed.length > 0 || sanitized.report.removed.length > 0) {
            console.info('数据已自动修复:', sanitized.report)
          }
        } else {
          throw new Error('数据无法修复，请检查数据格式')
        }
      } else {
        lineageData.value = data

        // 异步转换G6数据，避免阻塞UI
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            try {
              console.log('开始转换G6数据...')
              g6GraphData.value = transformToG6Data(data)
              console.log('G6数据转换完成')
              resolve()
            } catch (error) {
              console.error('G6数据转换失败:', error)
              resolve()
            }
          }, 10)
        })
      }

      // 清除错误状态
      clearError()

    } catch (err) {
      setError(err instanceof Error ? err.message : '设置血缘数据失败', err)
    }
  }

  /**
   * 解析SQL并生成血缘数据（增强版）
   * @param sql SQL语句
   */
  const parseSqlLineage = async (sql: string) => {
    if (!sql.trim()) {
      setError('SQL语句不能为空', null, 'validation')
      return
    }

    setLoading(true, '正在解析SQL语句...')
    clearError()
    retryCount.value = 0

    // 设置加载步骤
    loadingSteps.value = [
      { text: '验证SQL语法', status: 'active' },
      { text: '解析表结构', status: 'pending' },
      { text: '分析字段关系', status: 'pending' },
      { text: '生成血缘图', status: 'pending' }
    ]

    try {
      // 更新SQL文本
      sqlText.value = sql

      // 步骤1: 验证SQL语法
      updateLoadingStep(0, 'completed')
      updateLoadingStep(1, 'active')
      await new Promise(resolve => setTimeout(resolve, 300))

      // 步骤2: 解析表结构
      updateLoadingStep(1, 'completed')
      updateLoadingStep(2, 'active')
      await new Promise(resolve => setTimeout(resolve, 400))

      // 步骤3: 分析字段关系
      updateLoadingStep(2, 'completed')
      updateLoadingStep(3, 'active')
      await new Promise(resolve => setTimeout(resolve, 300))

      // 这里应该调用后端API解析SQL
      // 目前使用示例数据
      const sampleData = createSampleLineageData(sql)

      // 步骤4: 生成血缘图
      await setLineageData(sampleData)
      updateLoadingStep(3, 'completed')

    } catch (err) {
      // 标记当前步骤为错误
      const activeStepIndex = loadingSteps.value.findIndex(step => step.status === 'active')
      if (activeStepIndex >= 0) {
        updateLoadingStep(activeStepIndex, 'error')
      }

      setError(
        err instanceof Error ? err.message : '解析SQL失败',
        err,
        'business'
      )
    } finally {
      setLoading(false)
    }
  }

  /**
   * 设置加载状态
   */
  const setLoading = (isLoading: boolean, text?: string, progress?: number) => {
    loading.value = isLoading
    if (text) loadingText.value = text
    if (progress !== undefined) loadingProgress.value = progress

    if (!isLoading) {
      loadingSteps.value = []
      loadingProgress.value = undefined
    }
  }

  /**
   * 更新加载步骤
   */
  const updateLoadingStep = (index: number, status: 'pending' | 'active' | 'completed' | 'error') => {
    if (loadingSteps.value[index]) {
      loadingSteps.value[index].status = status
    }
  }

  /**
   * 设置错误状态
   */
  const setError = (message: string, details?: any, type: string = 'unknown') => {
    error.value = message
    errorDetails.value = details
    errorType.value = type
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
    errorDetails.value = null
    errorType.value = 'unknown'
    retryCount.value = 0
  }

  /**
   * 重试操作
   */
  const retryLastOperation = async () => {
    if (retryCount.value >= maxRetries.value) {
      setError('重试次数已达上限', null, 'system')
      return
    }

    retryCount.value++

    // 根据当前状态重试相应操作
    if (sqlText.value) {
      await parseSqlLineage(sqlText.value)
    }
  }

  /**
   * 加载示例数据
   */
  const loadSampleData = async () => {
    try {
      setLoading(true, '加载示例数据...')

      // 使用 setTimeout 将数据处理移到下一个事件循环，避免阻塞UI
      await new Promise<void>((resolve) => {
        setTimeout(async () => {
          try {
            updateLoadingStep('生成示例数据...', 0.3)
            const sampleData = createSampleLineageData()

            updateLoadingStep('设置血缘数据...', 0.6)
            await setLineageData(sampleData)
            sqlText.value = sampleData.metadata?.sqlText || ''

            updateLoadingStep('完成', 1.0)
            resolve()
          } catch (error) {
            console.error('示例数据处理失败:', error)
            resolve()
          }
        }, 10) // 短暂延迟让UI有机会更新
      })

    } catch (err) {
      setError(
        err instanceof Error ? err.message : '加载示例数据失败',
        err,
        'system'
      )
    } finally {
      setLoading(false)
    }
  }

  /**
   * 清空数据
   */
  const clearData = () => {
    lineageData.value = null
    g6GraphData.value = null
    sqlText.value = ''
    selectedNodes.value = []
    selectedEdges.value = []
    hoveredNode.value = null
    error.value = null
  }

  /**
   * 设置数据库类型
   * @param type 数据库类型
   */
  const setDatabaseType = (type: DatabaseType) => {
    databaseType.value = type
  }

  /**
   * 设置图谱配置
   * @param config 配置对象
   */
  const setGraphConfig = (config: Partial<typeof graphConfig.value>) => {
    graphConfig.value = { ...graphConfig.value, ...config }
  }

  /**
   * 切换字段级血缘显示
   */
  const toggleFieldLevelLineage = () => {
    showFieldLevelLineage.value = !showFieldLevelLineage.value
  }

  /**
   * 切换完整血缘链路显示
   */
  const toggleCompleteLineage = () => {
    showCompleteLineage.value = !showCompleteLineage.value
  }

  /**
   * 选中节点
   * @param nodeId 节点ID
   * @param multiple 是否多选
   */
  const selectNode = (nodeId: string, multiple: boolean = false) => {
    if (multiple) {
      if (selectedNodes.value.includes(nodeId)) {
        selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId)
      } else {
        selectedNodes.value.push(nodeId)
      }
    } else {
      selectedNodes.value = [nodeId]
    }
  }

  /**
   * 选中边
   * @param edgeId 边ID
   * @param multiple 是否多选
   */
  const selectEdge = (edgeId: string, multiple: boolean = false) => {
    if (multiple) {
      if (selectedEdges.value.includes(edgeId)) {
        selectedEdges.value = selectedEdges.value.filter(id => id !== edgeId)
      } else {
        selectedEdges.value.push(edgeId)
      }
    } else {
      selectedEdges.value = [edgeId]
    }
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedNodes.value = []
    selectedEdges.value = []
  }

  /**
   * 设置悬浮节点
   * @param nodeId 节点ID
   */
  const setHoveredNode = (nodeId: string | null) => {
    hoveredNode.value = nodeId
  }

  /**
   * 设置搜索关键词
   * @param keyword 关键词
   */
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
  }

  /**
   * 设置字段筛选配置
   * @param config 筛选配置
   */
  const setFieldFilter = (config: Partial<typeof fieldFilterConfig.value>) => {
    fieldFilterConfig.value = { ...fieldFilterConfig.value, ...config }
  }

  /**
   * 应用字段筛选
   * @param filter 筛选条件
   */
  const applyFieldFilter = (filter: {
    dataTypes?: string[]
    attributes?: string[]
    tables?: string[]
    fieldNamePattern?: string
  }) => {
    fieldFilterConfig.value = {
      enabled: true,
      dataTypes: filter.dataTypes || [],
      attributes: filter.attributes || [],
      tables: filter.tables || [],
      fieldNamePattern: filter.fieldNamePattern || ''
    }

    // 重新生成G6图数据，应用筛选
    if (lineageData.value) {
      const filteredData = applyFilterToLineageData(lineageData.value)
      // 这里需要调用数据转换工具
      // g6GraphData.value = transformLineageDataToG6(filteredData)
      console.log('筛选后的数据:', filteredData)
    }
  }

  /**
   * 清除字段筛选
   */
  const clearFieldFilter = () => {
    fieldFilterConfig.value = {
      enabled: false,
      dataTypes: [],
      attributes: [],
      tables: [],
      fieldNamePattern: ''
    }

    // 重新生成完整的G6图数据
    if (lineageData.value) {
      // g6GraphData.value = transformLineageDataToG6(lineageData.value)
      console.log('清除筛选，恢复完整数据:', lineageData.value)
    }
  }

  /**
   * 应用筛选到血缘数据
   * @param data 原始血缘数据
   * @returns 筛选后的血缘数据
   */
  const applyFilterToLineageData = (data: LineageData): LineageData => {
    if (!fieldFilterConfig.value.enabled) {
      return data
    }

    let filteredNodes = data.nodes

    // 按数据类型筛选
    if (fieldFilterConfig.value.dataTypes.length > 0) {
      filteredNodes = filteredNodes.filter(node => {
        const dataType = node.dataType?.type?.toLowerCase() || 'other'
        return fieldFilterConfig.value.dataTypes.some(type => {
          switch (type) {
            case 'string': return ['varchar', 'char', 'text', 'string'].some(t => dataType.includes(t))
            case 'number': return ['int', 'decimal', 'float', 'double', 'number'].some(t => dataType.includes(t))
            case 'date': return ['date', 'time', 'timestamp'].some(t => dataType.includes(t))
            case 'boolean': return ['bool', 'boolean'].some(t => dataType.includes(t))
            case 'json': return ['json', 'jsonb'].some(t => dataType.includes(t))
            case 'other': return !['varchar', 'char', 'text', 'string', 'int', 'decimal', 'float', 'double', 'number', 'date', 'time', 'timestamp', 'bool', 'boolean', 'json', 'jsonb'].some(t => dataType.includes(t))
            default: return false
          }
        })
      })
    }

    // 按字段属性筛选
    if (fieldFilterConfig.value.attributes.length > 0) {
      filteredNodes = filteredNodes.filter(node => {
        return fieldFilterConfig.value.attributes.some(attr => {
          switch (attr) {
            case 'primary': return node.isKey
            case 'nullable': return node.isNullable
            case 'indexed': return false // 暂时不支持
            case 'foreign': return false // 暂时不支持
            default: return false
          }
        })
      })
    }

    // 按表名筛选
    if (fieldFilterConfig.value.tables.length > 0) {
      filteredNodes = filteredNodes.filter(node =>
        fieldFilterConfig.value.tables.includes(node.tableName)
      )
    }

    // 按字段名模式筛选
    if (fieldFilterConfig.value.fieldNamePattern.trim()) {
      try {
        const regex = new RegExp(fieldFilterConfig.value.fieldNamePattern, 'i')
        filteredNodes = filteredNodes.filter(node => regex.test(node.fieldName))
      } catch (e) {
        // 如果正则表达式无效，使用简单的包含匹配
        const pattern = fieldFilterConfig.value.fieldNamePattern.toLowerCase()
        filteredNodes = filteredNodes.filter(node =>
          node.fieldName.toLowerCase().includes(pattern)
        )
      }
    }

    // 筛选相关的边
    const filteredNodeIds = new Set(filteredNodes.map(node => node.id))
    const filteredEdges = data.edges.filter(edge =>
      filteredNodeIds.has(edge.source) && filteredNodeIds.has(edge.target)
    )

    // 筛选相关的表
    const filteredTableNames = new Set(filteredNodes.map(node => node.tableName))
    const filteredTables: { [tableName: string]: TableInfo } = {}
    Object.keys(data.tables).forEach(tableName => {
      if (filteredTableNames.has(tableName)) {
        filteredTables[tableName] = data.tables[tableName]
      }
    })

    return {
      nodes: filteredNodes,
      edges: filteredEdges,
      tables: filteredTables,
      metadata: data.metadata
    }
  }

  /**
   * 切换主题
   * @param mode 主题模式
   */
  const setTheme = (mode: 'light' | 'dark') => {
    if (mode === 'dark') {
      theme.value = {
        mode: 'dark',
        colors: {
          primary: '#1890ff',
          secondary: '#52c41a',
          background: '#141414',
          surface: '#1f1f1f',
          text: '#ffffff',
          textSecondary: 'rgba(255, 255, 255, 0.85)',
          border: 'rgba(255, 255, 255, 0.08)',
          shadow: 'rgba(0, 0, 0, 0.3)',
          // 节点样式 - 深色主题
          nodeBackground: '#1f1f1f',
          nodeHeaderBackground: '#262626',
          nodeBorder: 'rgba(255, 255, 255, 0.08)',
          nodeText: '#ffffff',
          nodeFieldText: 'rgba(255, 255, 255, 0.85)',
          nodeShadow: 'rgba(0, 0, 0, 0.3)',
          // 边样式 - 深色主题
          edgeStroke: '#434343',
          edgeActiveStroke: '#1890ff',
          edgeLabel: 'rgba(255, 255, 255, 0.65)',
          // 交互状态 - 深色主题
          hover: '#262626',
          active: '#003a8c',
          selected: '#1890ff',
          // 字段状态颜色 - 深色主题
          fieldHover: '#262626',
          fieldActive: '#003a8c',
          fieldSelected: '#0050b3'
        }
      }
    } else {
      theme.value = {
        mode: 'light',
        colors: {
          primary: '#1890ff',
          secondary: '#52c41a',
          background: '#ffffff',
          surface: '#fafafa',
          text: '#262626',
          textSecondary: '#595959',
          border: 'rgba(0, 0, 0, 0.06)',
          shadow: 'rgba(0, 0, 0, 0.08)',
          // 节点样式 - 现代卡片设计
          nodeBackground: '#ffffff',
          nodeHeaderBackground: '#fafafa',
          nodeBorder: 'rgba(0, 0, 0, 0.06)',
          nodeText: '#262626',
          nodeFieldText: '#595959',
          nodeShadow: 'rgba(0, 0, 0, 0.08)',
          // 边样式 - 更柔和的颜色
          edgeStroke: '#d9d9d9',
          edgeActiveStroke: '#1890ff',
          edgeLabel: '#8c8c8c',
          // 交互状态 - 更明显的反馈
          hover: '#f5f5f5',
          active: '#e6f7ff',
          selected: '#1890ff',
          // 字段状态颜色
          fieldHover: '#f0f0f0',
          fieldActive: '#e6f7ff',
          fieldSelected: '#bae7ff'
        }
      }
    }
  }

  // 返回状态和方法
  return {
    // 状态
    lineageData,
    g6GraphData,
    sqlText,
    databaseType,
    loading,
    loadingText,
    loadingProgress,
    loadingSteps,
    error,
    errorDetails,
    errorType,
    retryCount,
    maxRetries,
    graphConfig,
    showFieldLevelLineage,
    showCompleteLineage,
    selectedNodes,
    selectedEdges,
    hoveredNode,
    searchKeyword,
    theme,
    fieldFilterConfig,

    // 计算属性
    tables,
    nodes,
    edges,
    tableCount,
    fieldCount,
    relationCount,
    searchResults,

    // 方法
    setLineageData,
    parseSqlLineage,
    loadSampleData,
    setLoading,
    updateLoadingStep,
    setError,
    clearError,
    retryLastOperation,
    clearData,
    setDatabaseType,
    setGraphConfig,
    toggleFieldLevelLineage,
    toggleCompleteLineage,
    selectNode,
    selectEdge,
    clearSelection,
    setHoveredNode,
    setSearchKeyword,
    setTheme,
    setFieldFilter,
    applyFieldFilter,
    clearFieldFilter
  }
})
