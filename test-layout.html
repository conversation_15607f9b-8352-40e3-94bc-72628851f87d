<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            overflow: hidden;
        }
        
        .test-layout {
            height: 100vh;
            width: 100%;
            display: flex;
            flex-direction: row;
        }
        
        .left-panel {
            width: 40%;
            max-width: 40%;
            min-width: 40%;
            flex: 0 0 40%;
            background: #fafafa;
            border-right: 1px solid #f0f0f0;
            padding: 20px;
            overflow-y: auto;
        }
        
        .right-panel {
            width: 60%;
            max-width: 60%;
            min-width: 60%;
            flex: 0 0 60%;
            background: #fff;
            padding: 20px;
            overflow-y: auto;
        }
        
        .panel-header {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #262626;
        }
        
        .panel-content {
            line-height: 1.6;
            color: #595959;
        }
        
        .size-info {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .test-layout {
                flex-direction: column;
            }
            
            .left-panel {
                width: 100%;
                max-width: 100%;
                min-width: 100%;
                flex: 0 0 auto;
                height: 40vh;
            }
            
            .right-panel {
                width: 100%;
                max-width: 100%;
                min-width: 100%;
                flex: 1 1 auto;
            }
        }
    </style>
</head>
<body>
    <div class="test-layout">
        <div class="left-panel">
            <div class="panel-header">左侧面板 (40%)</div>
            <div class="size-info" id="leftSize">宽度: 计算中...</div>
            <div class="panel-content">
                <h3>SQL 血缘分析</h3>
                <p>这里是左侧面板的内容区域，用于放置SQL编辑器和工具栏。</p>
                <br>
                <h4>功能特性：</h4>
                <ul>
                    <li>SQL语句编辑</li>
                    <li>数据库类型选择</li>
                    <li>解析血缘关系</li>
                    <li>工具栏控制</li>
                </ul>
                <br>
                <p>面板宽度应该始终保持在页面总宽度的40%。</p>
            </div>
        </div>
        
        <div class="right-panel">
            <div class="panel-header">右侧主内容区域 (60%)</div>
            <div class="size-info" id="rightSize">宽度: 计算中...</div>
            <div class="panel-content">
                <h3>数据血缘图谱</h3>
                <p>这里是右侧主内容区域，用于显示血缘图谱和相关控制。</p>
                <br>
                <h4>功能特性：</h4>
                <ul>
                    <li>血缘图谱渲染</li>
                    <li>交互控制</li>
                    <li>搜索功能</li>
                    <li>导出功能</li>
                    <li>设置面板</li>
                </ul>
                <br>
                <p>面板宽度应该始终保持在页面总宽度的60%。</p>
                <br>
                <h4>布局要求验证：</h4>
                <ul>
                    <li>✅ 左侧面板占40%宽度</li>
                    <li>✅ 右侧面板占60%宽度</li>
                    <li>✅ 总宽度占满100%</li>
                    <li>✅ 响应式设计支持</li>
                    <li>✅ 无边距影响</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function updateSizeInfo() {
            const leftPanel = document.querySelector('.left-panel');
            const rightPanel = document.querySelector('.right-panel');
            const leftSize = document.getElementById('leftSize');
            const rightSize = document.getElementById('rightSize');
            
            if (leftPanel && rightPanel && leftSize && rightSize) {
                const leftWidth = leftPanel.offsetWidth;
                const rightWidth = rightPanel.offsetWidth;
                const totalWidth = leftWidth + rightWidth;
                const windowWidth = window.innerWidth;
                
                const leftPercent = ((leftWidth / windowWidth) * 100).toFixed(1);
                const rightPercent = ((rightWidth / windowWidth) * 100).toFixed(1);
                
                leftSize.textContent = `宽度: ${leftWidth}px (${leftPercent}%)`;
                rightSize.textContent = `宽度: ${rightWidth}px (${rightPercent}%)`;
                
                console.log('布局信息:', {
                    左侧宽度: `${leftWidth}px (${leftPercent}%)`,
                    右侧宽度: `${rightWidth}px (${rightPercent}%)`,
                    总宽度: `${totalWidth}px`,
                    窗口宽度: `${windowWidth}px`,
                    比例: `${leftPercent}:${rightPercent}`
                });
            }
        }
        
        // 初始化
        updateSizeInfo();
        
        // 监听窗口大小变化
        window.addEventListener('resize', updateSizeInfo);
        
        // 定期更新（用于调试）
        setInterval(updateSizeInfo, 1000);
    </script>
</body>
</html>
