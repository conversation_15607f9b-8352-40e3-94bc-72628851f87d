<template>
  <div id="lineage-graph" class="lineage-graph">
    <!-- G6图谱将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { Graph } from '@antv/g6'

// 页面加载时直接执行核心渲染逻辑
onMounted(() => {
  fetch('https://assets.antv.antgroup.com/g6/dagre-combo.json')
    .then((res) => res.json())
    .then((data) => {
      const graph = new Graph({
        container: 'lineage-graph',
        autoFit: 'center',
        data,
        node: {
          type: 'rect',
          style: {
            size: [60, 30],
            radius: 8,
            labelText: (d) => d.id,
            labelBackground: true,
            ports: [{ placement: 'top' }, { placement: 'bottom' }],
          },
          palette: {
            field: (d) => d.combo,
          },
        },
        edge: {
          type: 'cubic-vertical',
          style: {
            endArrow: true,
          },
        },
        combo: {
          type: 'rect',
          style: {
            radius: 8,
            labelText: (d) => d.id,
          },
        },
        layout: {
          type: 'antv-dagre',
          ranksep: 50,
          nodesep: 5,
          sortByCombo: true,
        },
        behaviors: ['drag-element', 'drag-canvas', 'zoom-canvas'],
      });

      graph.render();
    });
});

// Props
interface Props {
  width?: number
  height?: number
  data?: G6GraphData | null
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600,
  data: null
})

// Emits
const emit = defineEmits<{
  graphReady: [graph: any]
  nodeClick: [nodeId: string, nodeData: any]
  edgeClick: [edgeId: string, edgeData: any]
  canvasClick: []
  fieldHover: [fieldId: string, fieldData: any, event: any]
  fieldClick: [fieldId: string, fieldData: any, event: any]
  fieldLeave: [fieldId: string]
  'trace-lineage': [fieldData: LineageNode, paths: any[]]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const graphInstance = ref<any>(null)
const isReady = ref(false)
const resizeObserver = ref<ResizeObserver | null>(null)

// Store
const lineageStore = useLineageStore()

// 字段交互状态
const hoveredFieldId = ref<string | null>(null)
const selectedFieldId = ref<string | null>(null)
const hoveredFieldData = ref<LineageNode | null>(null)
const showFieldTooltip = ref(false)

// 性能优化相关状态
const isLargeDataset = ref(false)
const virtualRenderingEnabled = ref(false)
const lazyLoadingEnabled = ref(false)
const performanceMode = ref<'normal' | 'optimized' | 'extreme'>('normal')
const visibleNodes = ref<Set<string>>(new Set())
const visibleEdges = ref<Set<string>>(new Set())
const renderQueue = ref<Array<() => void>>([])
const isRendering = ref(false)

// 转换数据格式为G6 v5格式
const transformData = (data: G6GraphData) => {
  return {
    nodes: data.nodes.map(node => ({
      id: node.id,
      data: {
        ...node
      }
    })),
    edges: data.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      data: {
        ...edge
      }
    }))
  }
}

// 性能优化：检测数据量并设置性能模式
const detectPerformanceMode = (data: G6GraphData) => {
  const nodeCount = data.nodes.length
  const edgeCount = data.edges.length

  isLargeDataset.value = nodeCount > 100 || edgeCount > 200

  if (nodeCount > 500 || edgeCount > 1000) {
    performanceMode.value = 'extreme'
    virtualRenderingEnabled.value = true
    lazyLoadingEnabled.value = true
  } else if (nodeCount > 100 || edgeCount > 200) {
    performanceMode.value = 'optimized'
    virtualRenderingEnabled.value = true
    lazyLoadingEnabled.value = false
  } else {
    performanceMode.value = 'normal'
    virtualRenderingEnabled.value = false
    lazyLoadingEnabled.value = false
  }

  console.log(`性能模式: ${performanceMode.value}, 节点数: ${nodeCount}, 边数: ${edgeCount}`)
}

// 虚拟渲染：计算可见区域内的节点和边
const calculateVisibleElements = () => {
  if (!graphInstance.value || !virtualRenderingEnabled.value) return

  try {
    const viewport = graphInstance.value.getViewport()
    const zoom = graphInstance.value.getZoom()
    const { x: centerX, y: centerY } = graphInstance.value.getViewCenter()

    // 计算可见区域的边界
    const viewWidth = props.width / zoom
    const viewHeight = props.height / zoom
    const left = centerX - viewWidth / 2
    const right = centerX + viewWidth / 2
    const top = centerY - viewHeight / 2
    const bottom = centerY + viewHeight / 2

    // 添加缓冲区以提前加载即将进入视野的元素
    const buffer = Math.max(viewWidth, viewHeight) * 0.2
    const bufferedLeft = left - buffer
    const bufferedRight = right + buffer
    const bufferedTop = top - buffer
    const bufferedBottom = bottom + buffer

    const newVisibleNodes = new Set<string>()
    const newVisibleEdges = new Set<string>()

    // 检查节点是否在可见区域内
    const allNodes = graphInstance.value.getAllNodesData()
    allNodes.forEach((node: any) => {
      const nodeData = node.data || node
      if (nodeData.x >= bufferedLeft && nodeData.x <= bufferedRight &&
          nodeData.y >= bufferedTop && nodeData.y <= bufferedBottom) {
        newVisibleNodes.add(nodeData.id)
      }
    })

    // 检查边是否连接到可见节点
    const allEdges = graphInstance.value.getAllEdgesData()
    allEdges.forEach((edge: any) => {
      const edgeData = edge.data || edge
      if (newVisibleNodes.has(edgeData.source) || newVisibleNodes.has(edgeData.target)) {
        newVisibleEdges.add(edgeData.id)
      }
    })

    visibleNodes.value = newVisibleNodes
    visibleEdges.value = newVisibleEdges

    console.log(`可见元素: ${newVisibleNodes.size} 节点, ${newVisibleEdges.size} 边`)
  } catch (error) {
    console.error('计算可见元素失败:', error)
  }
}

// 渲染队列管理：批量处理渲染任务
const processRenderQueue = throttle(() => {
  if (isRendering.value || renderQueue.value.length === 0) return

  isRendering.value = true

  try {
    // 批量处理渲染任务，每次最多处理10个
    const batchSize = performanceMode.value === 'extreme' ? 5 : 10
    const batch = renderQueue.value.splice(0, batchSize)

    batch.forEach(task => {
      try {
        task()
      } catch (error) {
        console.error('渲染任务执行失败:', error)
      }
    })

    // 如果还有任务，继续处理
    if (renderQueue.value.length > 0) {
      requestAnimationFrame(() => {
        isRendering.value = false
        processRenderQueue()
      })
    } else {
      isRendering.value = false
    }
  } catch (error) {
    console.error('处理渲染队列失败:', error)
    isRendering.value = false
  }
}, 16) // 约60fps

// 懒加载：分批加载图谱数据
const lazyLoadGraphData = (data: G6GraphData) => {
  if (!lazyLoadingEnabled.value) {
    // 非懒加载模式，直接设置所有数据
    return transformData(data)
  }

  console.log('启用懒加载模式')

  // 分批加载策略
  const batchSize = performanceMode.value === 'extreme' ? 20 : 50
  const totalNodes = data.nodes.length
  const totalEdges = data.edges.length

  // 首先加载核心节点（入度和出度较高的节点）
  const nodeImportance = new Map<string, number>()
  data.edges.forEach(edge => {
    nodeImportance.set(edge.source, (nodeImportance.get(edge.source) || 0) + 1)
    nodeImportance.set(edge.target, (nodeImportance.get(edge.target) || 0) + 1)
  })

  // 按重要性排序节点
  const sortedNodes = [...data.nodes].sort((a, b) => {
    const importanceA = nodeImportance.get(a.id) || 0
    const importanceB = nodeImportance.get(b.id) || 0
    return importanceB - importanceA
  })

  // 首批加载最重要的节点
  const initialNodes = sortedNodes.slice(0, batchSize)
  const initialNodeIds = new Set(initialNodes.map(n => n.id))

  // 加载与首批节点相关的边
  const initialEdges = data.edges.filter(edge =>
    initialNodeIds.has(edge.source) && initialNodeIds.has(edge.target)
  )

  // 将剩余数据加入渲染队列
  const remainingNodes = sortedNodes.slice(batchSize)
  const remainingEdges = data.edges.filter(edge =>
    !initialEdges.some(e => e.id === edge.id)
  )

  // 分批添加到渲染队列
  for (let i = 0; i < remainingNodes.length; i += batchSize) {
    const batch = remainingNodes.slice(i, i + batchSize)
    renderQueue.value.push(() => {
      if (graphInstance.value) {
        batch.forEach(node => {
          graphInstance.value.addData('node', {
            id: node.id,
            data: node
          })
        })
      }
    })
  }

  for (let i = 0; i < remainingEdges.length; i += batchSize) {
    const batch = remainingEdges.slice(i, i + batchSize)
    renderQueue.value.push(() => {
      if (graphInstance.value) {
        batch.forEach(edge => {
          graphInstance.value.addData('edge', {
            id: edge.id,
            source: edge.source,
            target: edge.target,
            data: edge
          })
        })
      }
    })
  }

  console.log(`懒加载: 首批${initialNodes.length}/${totalNodes}节点, ${initialEdges.length}/${totalEdges}边, 队列${renderQueue.value.length}任务`)

  // 启动渲染队列处理
  nextTick(() => {
    processRenderQueue()
  })

  return {
    nodes: initialNodes.map(node => ({
      id: node.id,
      data: node
    })),
    edges: initialEdges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      data: edge
    }))
  }
}

// 初始化图谱
const initGraph = async () => {
  if (!containerRef.value) {
    console.warn('Graph container not found')
    return
  }

  try {
    // 暂时注释掉自定义节点注册以排查问题
    // registerAllGraphElements()

    // 检测性能模式
    if (props.data) {
      detectPerformanceMode(props.data)
    }

    // 创建图谱实例配置
    // 使用any类型是因为G6的GraphOptions类型定义过于严格，与实际使用的配置结构不完全匹配
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const config: any = {
      container: containerRef.value,
      width: props.width,
      height: props.height,
      // 布局配置 - 使用dagre布局
      layout: {
        type: 'dagre',
        rankdir: 'LR', // 从左到右
        align: 'UL',
        nodesep: 80,
        ranksep: 150
      },
      // 默认节点样式 - 使用内置节点类型
      node: {
        type: 'rect',
        style: {
          fill: '#ffffff',
          stroke: 'rgba(0, 0, 0, 0.06)',
          lineWidth: 1,
          radius: 8,
          shadowColor: 'rgba(0, 0, 0, 0.08)',
          shadowBlur: 16,
          shadowOffsetX: 0,
          shadowOffsetY: 4
        }
      },
      // 默认边样式 - 使用内置边类型
      edge: {
        type: 'line',
        style: {
          stroke: '#1890ff',
          lineWidth: 2,
          opacity: 0.8,
          endArrow: {
            path: 'M 0,0 L 10,5 L 10,-5 Z',
            fill: '#1890ff',
            opacity: 0.9
          },
          labelText: (d: any) => d.data?.lineageEdge?.label || d.data?.label || '',
          labelFill: '#8c8c8c',
          labelFontSize: 11,
          labelBackground: {
            fill: '#ffffff',
            padding: [4, 8],
            radius: 4,
            stroke: 'rgba(0, 0, 0, 0.06)',
            lineWidth: 1
          },
          // 动画效果
          animate: true,
          animateCfg: {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
          }
        }
      },
      // 交互行为配置 - 性能优化
      behaviors: [
        {
          type: 'drag-canvas',
          enable: true,
          enableOptimize: true,
          allowDragOnItem: false,
          throttle: performanceMode.value === 'extreme' ? 32 : 16
        },
        {
          type: 'zoom-canvas',
          enable: true,
          enableOptimize: true,
          sensitivity: performanceMode.value === 'extreme' ? 0.8 : 1,
          minZoom: 0.1,
          maxZoom: 5,
          fixSelectedItems: {
            fixAll: false,
            fixLineWidth: false,
            fixLabel: false,
            fixState: 'selected'
          },
          throttle: performanceMode.value === 'extreme' ? 32 : 16
        },
        {
          type: 'drag-element',
          enable: true,
          enableTransient: performanceMode.value !== 'extreme',
          updateComboStructure: false,
          enableDelegate: performanceMode.value !== 'extreme',
          delegateStyle: {
            fillOpacity: 0.8,
            fill: '#1890ff',
            stroke: '#1890ff'
          },
          throttle: performanceMode.value === 'extreme' ? 32 : 16
        },
        {
          type: 'click-select',
          enable: true,
          multiple: performanceMode.value !== 'extreme',
          trigger: 'shift'
        }
      ],

      // 性能优化配置
      // G6 v5 默认使用canvas渲染器，不需要显式指定
      // renderer: () => new CanvasRenderer(),
      enabledStack: performanceMode.value !== 'extreme',
      animation: performanceMode.value === 'normal' ? {
        duration: 300,
        easing: 'ease-out'
      } : false,
      // 插件配置 - 暂时移除插件以排查问题
      plugins: []
    }

    // 如果有数据，添加到配置中
    if (props.data) {
      // 根据性能模式选择数据加载策略
      if (lazyLoadingEnabled.value) {
        config.data = lazyLoadGraphData(props.data)
      } else {
        config.data = transformData(props.data)
      }
    }

    graphInstance.value = new Graph(config)

    // 绑定事件
    bindEvents()

    // 绑定性能优化相关事件
    bindPerformanceEvents()

    // 渲染图谱
    graphInstance.value.render()

    isReady.value = true
    emit('graphReady', graphInstance.value)

    console.log(`G6 graph initialized successfully (${performanceMode.value} mode)`)
  } catch (error) {
    console.error('Failed to initialize G6 graph:', error)
  }
}

// 绑定事件
const bindEvents = () => {
  if (!graphInstance.value) return

  // 节点点击事件
  graphInstance.value.on('node:click', (evt: any) => {
    const nodeId = evt.itemId || evt.target?.id
    if (nodeId) {
      const nodeData = graphInstance.value?.getNodeData(nodeId)

      // 检查是否点击的是字段区域
      const fieldId = getFieldIdFromEvent(evt, nodeData)
      if (fieldId) {
        const fieldData = getFieldDataById(fieldId, nodeData)
        emit('fieldClick', fieldId, fieldData, evt)
      } else {
        emit('nodeClick', nodeId, nodeData)
      }
    }
  })

  // 节点悬浮事件
  graphInstance.value.on('node:pointerenter', (evt: any) => {
    const nodeId = evt.itemId || evt.target?.id
    if (nodeId) {
      const nodeData = graphInstance.value?.getNodeData(nodeId)

      // 检查是否悬浮在字段区域
      const fieldId = getFieldIdFromEvent(evt, nodeData)
      if (fieldId) {
        const fieldData = getFieldDataById(fieldId, nodeData)

        // 更新字段Tooltip状态
        hoveredFieldData.value = fieldData
        showFieldTooltip.value = true

        emit('fieldHover', fieldId, fieldData, evt)

        // 设置字段高亮状态
        setFieldHighlight(fieldId, true)
      }
    }
  })

  // 节点离开事件
  graphInstance.value.on('node:pointerleave', (evt: any) => {
    const nodeId = evt.itemId || evt.target?.id
    if (nodeId) {
      const nodeData = graphInstance.value?.getNodeData(nodeId)

      // 检查是否离开字段区域
      const fieldId = getFieldIdFromEvent(evt, nodeData)
      if (fieldId) {
        // 隐藏字段Tooltip
        showFieldTooltip.value = false
        hoveredFieldData.value = null

        emit('fieldLeave', fieldId)

        // 清除字段高亮状态
        setFieldHighlight(fieldId, false)
      }
    }
  })

  // 边点击事件
  graphInstance.value.on('edge:click', (evt: any) => {
    const edgeId = evt.itemId || evt.target?.id
    if (edgeId) {
      const edgeData = graphInstance.value?.getEdgeData(edgeId)
      emit('edgeClick', edgeId, edgeData)
    }
  })

  // 画布点击事件
  graphInstance.value.on('canvas:click', () => {
    emit('canvasClick')
  })
}

// 绑定性能优化相关事件
const bindPerformanceEvents = () => {
  if (!graphInstance.value) return

  // 视图变化事件 - 用于虚拟渲染
  const handleViewportChange = throttle(() => {
    if (virtualRenderingEnabled.value) {
      calculateVisibleElements()
    }
  }, performanceMode.value === 'extreme' ? 100 : 50)

  // 缩放事件
  graphInstance.value.on('viewportchange', handleViewportChange)
  graphInstance.value.on('zoom', handleViewportChange)

  // 拖拽事件 - 使用throttle优化性能
  const handleDrag = throttle((evt: any) => {
    if (virtualRenderingEnabled.value) {
      calculateVisibleElements()
    }
  }, performanceMode.value === 'extreme' ? 100 : 50)

  graphInstance.value.on('canvas:drag', handleDrag)

  // 内存清理事件
  const handleMemoryCleanup = debounce(() => {
    if (performanceMode.value === 'extreme') {
      // 清理不可见元素的缓存
      cleanupInvisibleElements()
    }
  }, 5000) // 5秒后清理

  graphInstance.value.on('afterrender', handleMemoryCleanup)
}

// 清理不可见元素的缓存
const cleanupInvisibleElements = () => {
  if (!graphInstance.value || !virtualRenderingEnabled.value) return

  try {
    const allNodes = graphInstance.value.getAllNodesData()
    const allEdges = graphInstance.value.getAllEdgesData()

    // 隐藏不可见的节点和边以释放内存
    allNodes.forEach((node: any) => {
      const nodeData = node.data || node
      if (!visibleNodes.value.has(nodeData.id)) {
        // 可以在这里实现更激进的内存清理策略
        // 例如移除DOM元素但保留数据
      }
    })

    allEdges.forEach((edge: any) => {
      const edgeData = edge.data || edge
      if (!visibleEdges.value.has(edgeData.id)) {
        // 类似的边清理策略
      }
    })

    console.log('内存清理完成')
  } catch (error) {
    console.error('内存清理失败:', error)
  }
}

// 字段事件处理辅助函数

/**
 * 从事件中获取字段ID
 * @param evt G6事件对象
 * @param nodeData 节点数据
 * @returns 字段ID或null
 */
const getFieldIdFromEvent = (evt: any, nodeData: any): string | null => {
  // 获取点击位置相对于节点的坐标
  const { x, y } = evt.canvas || { x: 0, y: 0 }
  const nodePosition = nodeData?.data?.x || 0
  const nodeY = nodeData?.data?.y || 0

  // 计算相对坐标
  const relativeX = x - nodePosition
  const relativeY = y - nodeY

  // 检查是否在字段区域内
  const fields = nodeData?.data?.fields || []
  const headerHeight = 40 // 表头高度
  const fieldHeight = 24 // 每个字段的高度
  const fieldPadding = 8 // 字段内边距

  if (relativeY > headerHeight) {
    const fieldIndex = Math.floor((relativeY - headerHeight - fieldPadding) / fieldHeight)
    if (fieldIndex >= 0 && fieldIndex < fields.length) {
      const field = fields[fieldIndex]
      return field.id
    }
  }

  return null
}

/**
 * 根据字段ID获取字段数据
 * @param fieldId 字段ID
 * @param nodeData 节点数据
 * @returns 字段数据或null
 */
const getFieldDataById = (fieldId: string, nodeData: any): any => {
  const fields = nodeData?.data?.fields || []
  return fields.find((field: any) => field.id === fieldId) || null
}

/**
 * 设置字段高亮状态
 * @param fieldId 字段ID
 * @param highlight 是否高亮
 */
const setFieldHighlight = (fieldId: string, highlight: boolean) => {
  if (!graphInstance.value) return

  try {
    if (highlight) {
      hoveredFieldId.value = fieldId
      // 更新状态管理中的悬浮字段
      lineageStore.setHoveredNode(fieldId)

      // 解析字段ID获取表名
      const [tableName] = fieldId.split('.')

      // 高亮相关节点 - 现代化效果
      const nodes = graphInstance.value.getAllNodesData()
      nodes.forEach((node: any) => {
        if (node.id === tableName) {
          // 高亮包含该字段的表节点
          graphInstance.value?.updateData('node', {
            id: node.id,
            style: {
              shadowBlur: 24,
              shadowColor: 'rgba(24, 144, 255, 0.4)',
              stroke: 'rgba(24, 144, 255, 0.6)',
              lineWidth: 2,
              opacity: 1,
              // 添加发光效果
              filter: 'drop-shadow(0 0 8px rgba(24, 144, 255, 0.3))'
            }
          })
        } else {
          // 降低其他节点的透明度
          graphInstance.value?.updateData('node', {
            id: node.id,
            style: {
              opacity: 0.5,
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.04)'
            }
          })
        }
      })

      // 高亮相关连线
      highlightRelatedEdges(fieldId, true)
    } else {
      hoveredFieldId.value = null
      lineageStore.setHoveredNode(null)

      // 重置所有节点样式
      const nodes = graphInstance.value.getAllNodesData()
      nodes.forEach((node: any) => {
        graphInstance.value?.updateData('node', {
          id: node.id,
          style: {
            opacity: 1,
            shadowBlur: 16,
            shadowColor: 'rgba(0, 0, 0, 0.08)',
            stroke: 'rgba(0, 0, 0, 0.06)',
            lineWidth: 1,
            filter: 'none'
          }
        })
      })

      // 清除连线高亮
      highlightRelatedEdges(fieldId, false)
    }

    // 触发图谱重新渲染
    graphInstance.value.render()
  } catch (error) {
    console.error('Failed to set field highlight:', error)
  }
}

/**
 * 高亮相关连线
 * @param fieldId 字段ID
 * @param highlight 是否高亮
 */
const highlightRelatedEdges = (fieldId: string, highlight: boolean) => {
  if (!graphInstance.value) return

  try {
    const edges = graphInstance.value.getAllEdgesData()

    edges.forEach((edge: any) => {
      const edgeData = edge.data
      const isRelated = edgeData?.sourceFieldId === fieldId ||
                       edgeData?.targetFieldId === fieldId

      if (isRelated && highlight) {
        // 高亮相关边 - 现代化效果
        const edgeStyle = {
          stroke: '#1890ff',
          lineWidth: 4,
          opacity: 1,
          shadowColor: 'rgba(24, 144, 255, 0.4)',
          shadowBlur: 8,
          // 添加动画效果
          animate: true,
          animateCfg: {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
          },
          // 箭头样式增强
          endArrow: {
            path: 'M 0,0 L 12,6 L 12,-6 Z',
            fill: '#1890ff',
            opacity: 1
          }
        }

        graphInstance.value.updateData('edge', {
          id: edge.id,
          style: edgeStyle
        })
      } else if (isRelated && !highlight) {
        // 重置相关边样式
        const edgeStyle = {
          stroke: edgeData?.style?.stroke || '#1890ff',
          lineWidth: edgeData?.style?.lineWidth || 2,
          opacity: 0.8,
          shadowColor: 'transparent',
          shadowBlur: 0,
          endArrow: {
            path: 'M 0,0 L 10,5 L 10,-5 Z',
            fill: edgeData?.style?.stroke || '#1890ff',
            opacity: 0.9
          }
        }

        graphInstance.value.updateData('edge', {
          id: edge.id,
          style: edgeStyle
        })
      } else if (!isRelated && highlight) {
        // 降低非相关边的透明度
        graphInstance.value.updateData('edge', {
          id: edge.id,
          style: {
            opacity: 0.3,
            lineWidth: (edgeData?.style?.lineWidth || 2) * 0.8
          }
        })
      } else if (!isRelated && !highlight) {
        // 重置非相关边样式
        graphInstance.value.updateData('edge', {
          id: edge.id,
          style: {
            opacity: 0.8,
            lineWidth: edgeData?.style?.lineWidth || 2
          }
        })
      }
    })
  } catch (error) {
    console.error('Failed to highlight related edges:', error)
  }
}

/**
 * 查找字段的血缘路径
 * @param fieldId 字段ID
 * @returns 血缘路径数组
 */
const findFieldLineagePaths = (fieldId: string) => {
  if (!graphInstance.value) return []

  try {
    const edges = graphInstance.value.getAllEdgesData()
    const paths: Array<{
      type: 'upstream' | 'downstream'
      path: string[]
      edges: string[]
    }> = []

    // 查找上游路径
    const upstreamPaths = findUpstreamPaths(fieldId, edges, [])
    paths.push(...upstreamPaths.map(path => ({
      type: 'upstream' as const,
      path: path.nodes,
      edges: path.edges
    })))

    // 查找下游路径
    const downstreamPaths = findDownstreamPaths(fieldId, edges, [])
    paths.push(...downstreamPaths.map(path => ({
      type: 'downstream' as const,
      path: path.nodes,
      edges: path.edges
    })))

    return paths
  } catch (error) {
    console.error('Failed to find lineage paths:', error)
    return []
  }
}

/**
 * 查找上游路径
 */
const findUpstreamPaths = (fieldId: string, edges: any[], visited: string[]): Array<{nodes: string[], edges: string[]}> => {
  if (visited.includes(fieldId)) return [] // 避免循环

  const paths: Array<{nodes: string[], edges: string[]}> = []
  const newVisited = [...visited, fieldId]

  // 查找指向当前字段的边
  const incomingEdges = edges.filter(edge => edge.data?.targetFieldId === fieldId)

  if (incomingEdges.length === 0) {
    // 没有上游，返回当前路径
    return [{ nodes: [fieldId], edges: [] }]
  }

  incomingEdges.forEach(edge => {
    const sourceField = edge.data?.sourceFieldId
    if (sourceField && !newVisited.includes(sourceField)) {
      const upstreamPaths = findUpstreamPaths(sourceField, edges, newVisited)
      upstreamPaths.forEach(path => {
        paths.push({
          nodes: [...path.nodes, fieldId],
          edges: [...path.edges, edge.id]
        })
      })
    }
  })

  return paths
}

/**
 * 查找下游路径
 */
const findDownstreamPaths = (fieldId: string, edges: any[], visited: string[]): Array<{nodes: string[], edges: string[]}> => {
  if (visited.includes(fieldId)) return [] // 避免循环

  const paths: Array<{nodes: string[], edges: string[]}> = []
  const newVisited = [...visited, fieldId]

  // 查找从当前字段出发的边
  const outgoingEdges = edges.filter(edge => edge.data?.sourceFieldId === fieldId)

  if (outgoingEdges.length === 0) {
    // 没有下游，返回当前路径
    return [{ nodes: [fieldId], edges: [] }]
  }

  outgoingEdges.forEach(edge => {
    const targetField = edge.data?.targetFieldId
    if (targetField && !newVisited.includes(targetField)) {
      const downstreamPaths = findDownstreamPaths(targetField, edges, newVisited)
      downstreamPaths.forEach(path => {
        paths.push({
          nodes: [fieldId, ...path.nodes],
          edges: [edge.id, ...path.edges]
        })
      })
    }
  })

  return paths
}

/**
 * 高亮血缘路径
 * @param paths 路径数组
 * @param highlight 是否高亮
 */
const highlightLineagePaths = (paths: any[], highlight: boolean) => {
  if (!graphInstance.value) return

  try {
    paths.forEach(path => {
      // 高亮路径中的所有边 - 现代化效果
      path.edges.forEach((edgeId: string) => {
        const edge = graphInstance.value?.getEdgeData(edgeId)
        if (edge) {
          const newStyle = highlight
            ? {
                stroke: path.type === 'upstream' ? '#1890ff' : '#52c41a',
                lineWidth: 5,
                opacity: 1,
                shadowColor: path.type === 'upstream' ? 'rgba(24, 144, 255, 0.5)' : 'rgba(82, 196, 26, 0.5)',
                shadowBlur: 12,
                // 添加渐变效果
                strokeOpacity: 0.9,
                // 增强箭头
                endArrow: {
                  path: 'M 0,0 L 14,7 L 14,-7 Z',
                  fill: path.type === 'upstream' ? '#1890ff' : '#52c41a',
                  opacity: 1,
                  shadowColor: path.type === 'upstream' ? 'rgba(24, 144, 255, 0.3)' : 'rgba(82, 196, 26, 0.3)',
                  shadowBlur: 6
                },
                // 动画效果
                animate: true,
                animateCfg: {
                  duration: 500,
                  easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
                }
              }
            : {
                stroke: edge.data?.style?.stroke || '#1890ff',
                lineWidth: edge.data?.style?.lineWidth || 2,
                opacity: 0.8,
                shadowColor: 'transparent',
                shadowBlur: 0,
                strokeOpacity: 1,
                endArrow: {
                  path: 'M 0,0 L 10,5 L 10,-5 Z',
                  fill: edge.data?.style?.stroke || '#1890ff',
                  opacity: 0.9
                }
              }

          graphInstance.value?.updateData('edge', {
            id: edgeId,
            style: newStyle
          })
        }
      })
    })

    // 触发重新渲染
    graphInstance.value.render()
  } catch (error) {
    console.error('Failed to highlight lineage paths:', error)
  }
}

// 字段Tooltip事件处理
const handleFieldDetails = (fieldData: LineageNode) => {
  console.log('查看字段详情:', fieldData)
  // 触发字段详情面板显示
  emit('fieldClick', fieldData.id, fieldData, null)
}

const handleFieldTraceLineage = (fieldData: LineageNode) => {
  console.log('追踪字段血缘:', fieldData)

  // 实现路径追踪功能
  if (graphInstance.value) {
    // 获取与该字段相关的所有路径
    const relatedPaths = findFieldLineagePaths(fieldData.id)

    // 高亮所有相关路径
    highlightLineagePaths(relatedPaths, true)

    // 触发路径追踪事件
    emit('trace-lineage', fieldData, relatedPaths)

    console.log('字段血缘路径:', relatedPaths)
  }
}

// 设置图谱数据
const setGraphData = async (data: G6GraphData) => {
  if (!graphInstance.value || !data) return

  try {
    // 检测性能模式
    detectPerformanceMode(data)

    // 异步应用Dagre布局，避免阻塞UI
    const layoutOptions: DagreLayoutOptions = {
      rankdir: 'LR',
      align: 'UL',
      nodesep: 80,
      ranksep: 150,
      edgesep: 10,
      enableOptimization: true,
      largeDataThreshold: 100
    }

    console.log('开始异步布局计算...')
    const layoutedData = await applyDagreLayoutAsync(data, layoutOptions)
    console.log('布局计算完成')

    // 根据性能模式选择数据加载策略
    let g6Data
    if (lazyLoadingEnabled.value) {
      // 清空渲染队列
      renderQueue.value = []
      g6Data = lazyLoadGraphData(layoutedData)
    } else {
      g6Data = transformData(layoutedData)
    }

    // 设置新数据
    graphInstance.value.setData(g6Data)

    // 渲染图谱
    if (performanceMode.value === 'extreme') {
      // 极端模式下使用requestAnimationFrame优化渲染
      requestAnimationFrame(() => {
        if (graphInstance.value) {
          graphInstance.value.render()
        }
      })
    } else {
      graphInstance.value.render()
    }

    // 自适应画布
    nextTick(() => {
      if (graphInstance.value) {
        if (performanceMode.value === 'extreme') {
          // 极端模式下延迟fitView以避免性能问题
          setTimeout(() => {
            if (graphInstance.value) {
              graphInstance.value.fitView()
            }
          }, 100)
        } else {
          graphInstance.value.fitView()
        }
      }
    })

    console.log(`Graph data updated (${performanceMode.value} mode):`, layoutedData)
  } catch (error) {
    console.error('Failed to set graph data:', error)
  }
}

// 调整图谱尺寸
const resizeGraph = (width: number, height: number) => {
  if (!graphInstance.value) return

  try {
    // 在调整尺寸前暂停事件处理
    const wasEnabled = graphInstance.value.getPlugins().some((plugin: any) => plugin.type === 'drag-element')

    // 调整尺寸
    graphInstance.value.setSize(width, height)

    // 使用 nextTick 确保 DOM 更新完成后再进行其他操作
    nextTick(() => {
      if (graphInstance.value) {
        try {
          graphInstance.value.fitView()
        } catch (error) {
          console.warn('FitView failed after resize, retrying...', error)
          // 如果 fitView 失败，延迟重试
          setTimeout(() => {
            if (graphInstance.value) {
              try {
                graphInstance.value.fitView()
              } catch (retryError) {
                console.error('FitView retry failed:', retryError)
              }
            }
          }, 100)
        }
      }
    })
  } catch (error) {
    console.error('Failed to resize graph:', error)
  }
}

// 缩放控制方法
const zoomTo = (ratio: number, center?: { x: number; y: number }) => {
  if (!graphInstance.value) return

  try {
    if (center) {
      graphInstance.value.zoomTo(ratio, center)
    } else {
      graphInstance.value.zoomTo(ratio)
    }
  } catch (error) {
    console.error('Failed to zoom graph:', error)
  }
}

const zoomIn = () => {
  if (!graphInstance.value) return

  try {
    const currentZoom = graphInstance.value.getZoom()
    const newZoom = Math.min(currentZoom * 1.2, 5) // 最大缩放5倍
    graphInstance.value.zoomTo(newZoom)
  } catch (error) {
    console.error('Failed to zoom in:', error)
  }
}

const zoomOut = () => {
  if (!graphInstance.value) return

  try {
    const currentZoom = graphInstance.value.getZoom()
    const newZoom = Math.max(currentZoom / 1.2, 0.1) // 最小缩放0.1倍
    graphInstance.value.zoomTo(newZoom)
  } catch (error) {
    console.error('Failed to zoom out:', error)
  }
}

// 平移控制方法
const translateTo = (x: number, y: number) => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.translateTo(x, y)
  } catch (error) {
    console.error('Failed to translate graph:', error)
  }
}

const translateBy = (dx: number, dy: number) => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.translate(dx, dy)
  } catch (error) {
    console.error('Failed to translate graph:', error)
  }
}

// 适应视图
const fitView = (padding?: number | number[]) => {
  if (!graphInstance.value) return

  try {
    const fitPadding = padding || 20
    graphInstance.value.fitView(fitPadding)
  } catch (error) {
    console.error('Failed to fit view:', error)
  }
}

// 重置视图
const resetView = () => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.zoomTo(1)
    graphInstance.value.translateTo(0, 0)
    graphInstance.value.fitView()
  } catch (error) {
    console.error('Failed to reset view:', error)
  }
}

// 重新应用布局
const reapplyLayout = (options?: DagreLayoutOptions) => {
  if (!graphInstance.value || !props.data) return

  try {
    const layoutOptions: DagreLayoutOptions = {
      rankdir: 'LR',
      align: 'UL',
      nodesep: 80,
      ranksep: 150,
      edgesep: 10,
      enableOptimization: true,
      largeDataThreshold: 100,
      ...options
    }

    // 重新应用布局
    const layoutedData = applyDagreLayout(props.data, layoutOptions)
    const g6Data = transformData(layoutedData)

    graphInstance.value.setData(g6Data)
    graphInstance.value.render()

    nextTick(() => {
      if (graphInstance.value) {
        graphInstance.value.fitView()
      }
    })

    console.log('Layout reapplied with options:', layoutOptions)
  } catch (error) {
    console.error('Failed to reapply layout:', error)
  }
}

// 切换布局方向
const changeLayoutDirection = (direction: 'LR' | 'TB' | 'RL' | 'BT') => {
  reapplyLayout({ rankdir: direction })
}

// 调整布局间距
const adjustLayoutSpacing = (nodeSpacing: number, rankSpacing: number) => {
  reapplyLayout({
    nodesep: nodeSpacing,
    ranksep: rankSpacing
  })
}

// 获取当前缩放比例
const getCurrentZoom = (): number => {
  if (!graphInstance.value) return 1

  try {
    return graphInstance.value.getZoom()
  } catch (error) {
    console.error('Failed to get current zoom:', error)
    return 1
  }
}

// 获取当前视图中心点
const getViewCenter = (): { x: number; y: number } => {
  if (!graphInstance.value) return { x: 0, y: 0 }

  try {
    const { width, height } = graphInstance.value.getSize()
    return { x: width / 2, y: height / 2 }
  } catch (error) {
    console.error('Failed to get view center:', error)
    return { x: 0, y: 0 }
  }
}

// 销毁图谱
const destroyGraph = () => {
  if (graphInstance.value) {
    try {
      // 清理所有事件监听器
      graphInstance.value.off()

      // 清理渲染队列
      renderQueue.value = []
      isRendering.value = false

      // 销毁图谱实例
      graphInstance.value.destroy()
      graphInstance.value = null
      isReady.value = false

      console.log('Graph destroyed successfully')
    } catch (error) {
      console.error('Error destroying graph:', error)
      // 强制清理
      graphInstance.value = null
      isReady.value = false
    }
  }
}

// 设置响应式监听
const setupResizeObserver = () => {
  if (!containerRef.value || !window.ResizeObserver) return

  // 使用防抖来避免频繁的尺寸调整
  const debouncedResize = debounce((width: number, height: number) => {
    if (isReady.value && graphInstance.value) {
      resizeGraph(width, height)
    }
  }, 150)

  resizeObserver.value = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect
      if (width > 0 && height > 0) {
        debouncedResize(width, height)
      }
    }
  })

  resizeObserver.value.observe(containerRef.value)
}

// 清理响应式监听
const cleanupResizeObserver = () => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData && isReady.value) {
      setGraphData(newData)
    }
  },
  { deep: true }
)

// 监听尺寸变化
watch(
  () => [props.width, props.height],
  ([newWidth, newHeight]) => {
    if (isReady.value && graphInstance.value) {
      // 使用防抖避免频繁调整
      const debouncedResize = debounce(() => {
        if (isReady.value && graphInstance.value) {
          resizeGraph(newWidth, newHeight)
        }
      }, 100)
      debouncedResize()
    }
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGraph().then(() => {
      // 图谱初始化完成后设置响应式监听
      setupResizeObserver()
    })
  })
})

onUnmounted(() => {
  cleanupResizeObserver()
  destroyGraph()
})

// 搜索定位功能
const searchAndLocate = (keyword: string, type: 'table' | 'field' | 'all' = 'all') => {
  if (!graphInstance.value || !keyword.trim()) return []

  const results: any[] = []
  const lowerKeyword = keyword.toLowerCase()

  try {
    const allNodes = graphInstance.value.getAllNodesData()

    allNodes.forEach((nodeData: any) => {
      const node = nodeData.data || nodeData

      // 搜索表名
      if ((type === 'table' || type === 'all') && node.tableName) {
        if (node.tableName.toLowerCase().includes(lowerKeyword)) {
          results.push({
            type: 'table',
            id: nodeData.id,
            name: node.tableName,
            nodeData: nodeData,
            matchScore: node.tableName.toLowerCase().indexOf(lowerKeyword) === 0 ? 1 : 0.8
          })
        }
      }

      // 搜索字段名
      if ((type === 'field' || type === 'all') && node.fieldName) {
        if (node.fieldName.toLowerCase().includes(lowerKeyword)) {
          results.push({
            type: 'field',
            id: nodeData.id,
            name: node.fieldName,
            tableName: node.tableName,
            nodeData: nodeData,
            matchScore: node.fieldName.toLowerCase().indexOf(lowerKeyword) === 0 ? 1 : 0.8
          })
        }
      }
    })

    // 按匹配度排序
    return results.sort((a, b) => b.matchScore - a.matchScore)
  } catch (error) {
    console.error('Search failed:', error)
    return []
  }
}

// 定位到指定节点
const locateToNode = (nodeId: string, highlight: boolean = true) => {
  if (!graphInstance.value) return false

  try {
    const nodeData = graphInstance.value.getNodeData(nodeId)
    if (!nodeData) return false

    // 获取节点位置
    const nodePosition = graphInstance.value.getNodePosition(nodeId)
    if (!nodePosition) return false

    // 移动视图到节点位置
    const canvasSize = graphInstance.value.getSize()
    const centerX = canvasSize[0] / 2
    const centerY = canvasSize[1] / 2

    // 计算需要移动的距离
    const deltaX = centerX - nodePosition.x
    const deltaY = centerY - nodePosition.y

    // 平滑移动到目标位置
    graphInstance.value.translateBy(deltaX, deltaY, true, {
      duration: 500,
      easing: 'easeInOutCubic'
    })

    // 高亮节点
    if (highlight) {
      // 清除之前的高亮
      graphInstance.value.setItemState(nodeId, 'highlight', false)
      graphInstance.value.setItemState(nodeId, 'selected', false)

      // 设置新的高亮
      setTimeout(() => {
        graphInstance.value.setItemState(nodeId, 'highlight', true)
        graphInstance.value.setItemState(nodeId, 'selected', true)

        // 3秒后清除高亮
        setTimeout(() => {
          graphInstance.value.setItemState(nodeId, 'highlight', false)
          graphInstance.value.setItemState(nodeId, 'selected', false)
        }, 3000)
      }, 100)
    }

    return true
  } catch (error) {
    console.error('Failed to locate node:', error)
    return false
  }
}

// 导出图谱为图片
const exportAsImage = (format: 'png' | 'jpeg' = 'png', fileName?: string) => {
  if (!graphInstance.value) return null

  try {
    // 获取图谱的画布元素
    const canvas = graphInstance.value.getCanvas().getContextService().getDomElement() as HTMLCanvasElement
    if (!canvas) return null

    // 创建下载链接
    const link = document.createElement('a')
    link.download = fileName || `lineage-graph.${format}`
    link.href = canvas.toDataURL(`image/${format}`)

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    return link.href
  } catch (error) {
    console.error('Failed to export image:', error)
    return null
  }
}

// 导出图谱为PDF
const exportAsPDF = (fileName?: string) => {
  if (!graphInstance.value) return null

  try {
    // 这里需要使用jsPDF库，暂时返回图片数据URL
    const canvas = graphInstance.value.getCanvas().getContextService().getDomElement() as HTMLCanvasElement
    if (!canvas) return null

    // 获取图片数据
    const imgData = canvas.toDataURL('image/png')

    // 创建一个简单的PDF下载（实际项目中应该使用jsPDF）
    const link = document.createElement('a')
    link.download = fileName || 'lineage-graph.png'
    link.href = imgData

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    return imgData
  } catch (error) {
    console.error('Failed to export PDF:', error)
    return null
  }
}

// MiniMap控制功能
const showMiniMap = ref(true)

const toggleMiniMap = () => {
  if (!graphInstance.value) return

  try {
    const minimapPlugin = graphInstance.value.getPluginInstance('minimap')
    if (minimapPlugin) {
      if (showMiniMap.value) {
        minimapPlugin.hide()
      } else {
        minimapPlugin.show()
      }
      showMiniMap.value = !showMiniMap.value
    }
  } catch (error) {
    console.error('Failed to toggle minimap:', error)
  }
}

// 重置布局功能增强
const resetLayout = () => {
  if (!graphInstance.value) return false

  try {
    // 重新应用布局
    graphInstance.value.layout()

    // 适应视图
    nextTick(() => {
      if (graphInstance.value) {
        graphInstance.value.fitView()
      }
    })

    return true
  } catch (error) {
    console.error('Failed to reset layout:', error)
    return false
  }
}

/**
 * 字段筛选功能
 */
const currentFilter = ref<{
  dataTypes: string[]
  attributes: string[]
  tables: string[]
  fieldNamePattern: string
} | null>(null)

const filteredGraphData = ref<G6GraphData | null>(null)

/**
 * 应用字段筛选
 */
const applyFieldFilter = (filter: {
  dataTypes?: string[]
  attributes?: string[]
  tables?: string[]
  fieldNamePattern?: string
}) => {
  if (!props.data) return

  currentFilter.value = {
    dataTypes: filter.dataTypes || [],
    attributes: filter.attributes || [],
    tables: filter.tables || [],
    fieldNamePattern: filter.fieldNamePattern || ''
  }

  // 筛选节点
  const filteredNodes = props.data.nodes.filter((node: any) => {
    // 如果是表节点，检查是否在表筛选列表中
    if (node.nodeType === 'table') {
      if (currentFilter.value!.tables.length > 0) {
        return currentFilter.value!.tables.includes(node.tableName || '')
      }
      return true
    }

    // 如果是字段节点，应用字段筛选
    if (node.nodeType === 'field') {
      // 数据类型筛选
      if (currentFilter.value!.dataTypes.length > 0) {
        if (!currentFilter.value!.dataTypes.includes(node.dataType || '')) {
          return false
        }
      }

      // 字段名模式筛选
      if (currentFilter.value!.fieldNamePattern) {
        const pattern = currentFilter.value!.fieldNamePattern.toLowerCase()
        const fieldName = (node.fieldName || '').toLowerCase()
        if (!fieldName.includes(pattern)) {
          return false
        }
      }

      // 表名筛选
      if (currentFilter.value!.tables.length > 0) {
        if (!currentFilter.value!.tables.includes(node.tableName || '')) {
          return false
        }
      }
    }

    return true
  })

  // 筛选边 - 只保留连接到筛选后节点的边
  const filteredNodeIds = new Set(filteredNodes.map((n: any) => n.id))
  const filteredEdges = props.data.edges.filter((edge: any) =>
    filteredNodeIds.has(edge.source) && filteredNodeIds.has(edge.target)
  )

  filteredGraphData.value = {
    nodes: filteredNodes,
    edges: filteredEdges
  }

  // 更新图谱显示
  if (graphInstance.value) {
    setGraphData(filteredGraphData.value)
  }

  console.log('应用字段筛选:', {
    原始节点数: props.data.nodes.length,
    筛选后节点数: filteredNodes.length,
    原始边数: props.data.edges.length,
    筛选后边数: filteredEdges.length
  })
}

/**
 * 清除字段筛选
 */
const clearFieldFilter = () => {
  currentFilter.value = null
  filteredGraphData.value = null

  // 恢复原始数据
  if (props.data && graphInstance.value) {
    setGraphData(props.data)
  }

  console.log('清除字段筛选，恢复原始数据')
}

/**
 * 获取当前筛选后的数据
 */
const getFilteredData = () => {
  return filteredGraphData.value || props.data
}

// 暴露方法给父组件
defineExpose({
  graph: graphInstance,
  isReady,
  setGraphData,
  resizeGraph,
  destroyGraph,
  // 交互控制方法
  zoomTo,
  zoomIn,
  zoomOut,
  translateTo,
  translateBy,
  fitView,
  resetView,
  getCurrentZoom,
  getViewCenter,
  // 字段交互方法
  setFieldHighlight,
  highlightRelatedEdges,
  findFieldLineagePaths,
  highlightLineagePaths,
  hoveredFieldId,
  selectedFieldId,
  hoveredFieldData,
  showFieldTooltip,
  // 搜索和导出方法
  searchAndLocate,
  locateToNode,
  exportAsImage,
  exportAsPDF,
  // MiniMap和布局方法
  toggleMiniMap,
  showMiniMap,
  resetLayout,
  // 新增的Dagre布局控制方法
  reapplyLayout,
  changeLayoutDirection,
  adjustLayoutSpacing,
  // 性能优化方法
  performanceMode,
  isLargeDataset,
  virtualRenderingEnabled,
  lazyLoadingEnabled,
  visibleNodes,
  visibleEdges,
  calculateVisibleElements,
  processRenderQueue,
  // 字段筛选方法
  applyFieldFilter,
  clearFieldFilter,
  getFilteredData,
  cleanupInvisibleElements
})
</script>

<style scoped>
.lineage-graph {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.02);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .lineage-graph {
    background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  }
}
</style>
