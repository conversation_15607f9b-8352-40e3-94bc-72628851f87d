/**
 * 演示数据生成器
 * 用于生成各种场景的示例血缘数据
 */

import type { LineageData, LineageNode, LineageEdge, TableInfo } from '@/types/lineage'
import { DatabaseType } from '@/types/lineage'

// 转换类型枚举
export enum TransformType {
  DIRECT = 'DIRECT',           // 直接映射
  ALIAS = 'ALIAS',             // 别名转换
  CALCULATION = 'CALCULATION', // 计算转换
  AGGREGATION = 'AGGREGATION', // 聚合转换
  JOIN = 'JOIN',               // 连接转换
  UNION = 'UNION',             // 联合转换
  FILTER = 'FILTER'            // 过滤转换
}

// 字段信息接口
export interface FieldInfo {
  id: string
  fieldName: string
  tableName: string
  type: 'field'
  dataType: {
    type: string
    length?: number
    precision?: number
    scale?: number
    isPrimaryKey?: boolean
    isForeignKey?: boolean
    isNullable?: boolean
    enumValues?: string[]
  }
  description?: string
  isPrimaryKey?: boolean
  isForeignKey?: boolean
  isNullable?: boolean
  defaultValue?: any
  label: string
}

// 演示场景类型
export type DemoScenario =
  | 'basic'           // 基础示例
  | 'ecommerce'       // 电商场景
  | 'finance'         // 金融场景
  | 'supply-chain'    // 供应链场景
  | 'analytics'       // 数据分析场景
  | 'complex'         // 复杂查询场景

/**
 * 生成演示数据
 */
export function generateDemoData(scenario: DemoScenario): LineageData {
  switch (scenario) {
    case 'basic':
      return generateBasicDemo()
    case 'ecommerce':
      return generateEcommerceDemo()
    case 'finance':
      return generateFinanceDemo()
    case 'supply-chain':
      return generateSupplyChainDemo()
    case 'analytics':
      return generateAnalyticsDemo()
    case 'complex':
      return generateComplexDemo()
    default:
      return generateBasicDemo()
  }
}

/**
 * 基础示例数据
 */
function generateBasicDemo(): LineageData {
  const tables: Record<string, TableInfo> = {
    users: {
      name: 'users',
      type: 'table',
      description: '用户表',
      schema: 'public',
      database: 'demo_db',
      fields: [
        createField('users.id', 'id', 'users', 'INT', { isPrimaryKey: true }),
        createField('users.name', 'name', 'users', 'VARCHAR', { length: 100 }),
        createField('users.email', 'email', 'users', 'VARCHAR', { length: 255 }),
        createField('users.created_at', 'created_at', 'users', 'TIMESTAMP')
      ]
    },
    orders: {
      name: 'orders',
      type: 'table',
      description: '订单表',
      schema: 'public',
      database: 'demo_db',
      fields: [
        createField('orders.id', 'id', 'orders', 'INT', { isPrimaryKey: true }),
        createField('orders.user_id', 'user_id', 'orders', 'INT', { isForeignKey: true }),
        createField('orders.total_amount', 'total_amount', 'orders', 'DECIMAL', { precision: 10, scale: 2 }),
        createField('orders.order_date', 'order_date', 'orders', 'TIMESTAMP')
      ]
    }
  }

  const nodes: LineageNode[] = [
    ...tables.users.fields,
    ...tables.orders.fields
  ]

  const edges: LineageEdge[] = [
    createEdge('edge_1', 'users.id', 'orders.user_id', TransformType.JOIN, '用户关联', 1.0)
  ]

  return {
    tables,
    nodes,
    edges,
    metadata: {
      version: '1.0.0',
      parseTime: new Date().toISOString(),
      sqlText: 'SELECT u.id, u.name, o.total_amount FROM users u JOIN orders o ON u.id = o.user_id'
    }
  }
}

/**
 * 电商场景示例数据
 */
function generateEcommerceDemo(): LineageData {
  const tables: Record<string, TableInfo> = {
    users: {
      name: 'users',
      type: 'table',
      description: '用户表',
      fields: [
        createField('users.id', 'id', 'users', 'INT', { isPrimaryKey: true }),
        createField('users.name', 'name', 'users', 'VARCHAR', { length: 100 }),
        createField('users.email', 'email', 'users', 'VARCHAR', { length: 255 }),
        createField('users.phone', 'phone', 'users', 'VARCHAR', { length: 20 }),
        createField('users.city', 'city', 'users', 'VARCHAR', { length: 50 }),
        createField('users.registration_date', 'registration_date', 'users', 'TIMESTAMP')
      ]
    },
    orders: {
      name: 'orders',
      type: 'table',
      description: '订单表',
      fields: [
        createField('orders.id', 'id', 'orders', 'INT', { isPrimaryKey: true }),
        createField('orders.user_id', 'user_id', 'orders', 'INT', { isForeignKey: true }),
        createField('orders.order_date', 'order_date', 'orders', 'TIMESTAMP'),
        createField('orders.total_amount', 'total_amount', 'orders', 'DECIMAL', { precision: 10, scale: 2 }),
        createField('orders.status', 'status', 'orders', 'VARCHAR', { length: 20 })
      ]
    },
    order_items: {
      name: 'order_items',
      type: 'table',
      description: '订单项表',
      fields: [
        createField('order_items.id', 'id', 'order_items', 'INT', { isPrimaryKey: true }),
        createField('order_items.order_id', 'order_id', 'order_items', 'INT', { isForeignKey: true }),
        createField('order_items.product_id', 'product_id', 'order_items', 'INT', { isForeignKey: true }),
        createField('order_items.quantity', 'quantity', 'order_items', 'INT'),
        createField('order_items.unit_price', 'unit_price', 'order_items', 'DECIMAL', { precision: 8, scale: 2 })
      ]
    },
    products: {
      name: 'products',
      type: 'table',
      description: '产品表',
      fields: [
        createField('products.id', 'id', 'products', 'INT', { isPrimaryKey: true }),
        createField('products.name', 'name', 'products', 'VARCHAR', { length: 200 }),
        createField('products.category_id', 'category_id', 'products', 'INT', { isForeignKey: true }),
        createField('products.price', 'price', 'products', 'DECIMAL', { precision: 8, scale: 2 }),
        createField('products.description', 'description', 'products', 'TEXT')
      ]
    },
    categories: {
      name: 'categories',
      type: 'table',
      description: '产品分类表',
      fields: [
        createField('categories.id', 'id', 'categories', 'INT', { isPrimaryKey: true }),
        createField('categories.name', 'name', 'categories', 'VARCHAR', { length: 100 }),
        createField('categories.parent_id', 'parent_id', 'categories', 'INT', { isForeignKey: true })
      ]
    },
    user_order_summary: {
      name: 'user_order_summary',
      type: 'view',
      description: '用户订单汇总视图',
      fields: [
        createField('user_order_summary.user_id', 'user_id', 'user_order_summary', 'INT'),
        createField('user_order_summary.user_name', 'user_name', 'user_order_summary', 'VARCHAR', { length: 100 }),
        createField('user_order_summary.total_orders', 'total_orders', 'user_order_summary', 'INT'),
        createField('user_order_summary.total_spent', 'total_spent', 'user_order_summary', 'DECIMAL', { precision: 12, scale: 2 }),
        createField('user_order_summary.avg_order_value', 'avg_order_value', 'user_order_summary', 'DECIMAL', { precision: 10, scale: 2 })
      ]
    }
  }

  const nodes: LineageNode[] = Object.values(tables).flatMap(table => table.fields)

  const edges: LineageEdge[] = [
    // 基础关联
    createEdge('edge_1', 'users.id', 'orders.user_id', TransformType.JOIN, '用户-订单关联', 1.0),
    createEdge('edge_2', 'orders.id', 'order_items.order_id', TransformType.JOIN, '订单-订单项关联', 1.0),
    createEdge('edge_3', 'products.id', 'order_items.product_id', TransformType.JOIN, '产品-订单项关联', 1.0),
    createEdge('edge_4', 'categories.id', 'products.category_id', TransformType.JOIN, '分类-产品关联', 1.0),

    // 视图字段映射
    createEdge('edge_5', 'users.id', 'user_order_summary.user_id', TransformType.DIRECT, '用户ID映射', 1.0),
    createEdge('edge_6', 'users.name', 'user_order_summary.user_name', TransformType.DIRECT, '用户名映射', 1.0),
    createEdge('edge_7', 'orders.id', 'user_order_summary.total_orders', TransformType.AGGREGATION, '订单数量聚合', 0.9),
    createEdge('edge_8', 'orders.total_amount', 'user_order_summary.total_spent', TransformType.AGGREGATION, '总消费聚合', 0.9),
    createEdge('edge_9', 'orders.total_amount', 'user_order_summary.avg_order_value', TransformType.AGGREGATION, '平均订单价值', 0.9)
  ]

  return {
    tables,
    nodes,
    edges,
    metadata: {
      version: '1.0.0',
      parseTime: new Date().toISOString(),
      sqlText: `
        SELECT
          u.id AS user_id,
          u.name AS user_name,
          COUNT(o.id) AS total_orders,
          SUM(o.total_amount) AS total_spent,
          AVG(o.total_amount) AS avg_order_value
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        GROUP BY u.id, u.name
      `
    }
  }
}

/**
 * 金融场景示例数据
 */
function generateFinanceDemo(): LineageData {
  const tables: Record<string, TableInfo> = {
    customers: {
      name: 'customers',
      type: 'table',
      description: '客户表',
      fields: [
        createField('customers.id', 'id', 'customers', 'INT', { isPrimaryKey: true }),
        createField('customers.name', 'name', 'customers', 'VARCHAR', { length: 100 }),
        createField('customers.credit_score', 'credit_score', 'customers', 'INT'),
        createField('customers.risk_level', 'risk_level', 'customers', 'VARCHAR', { length: 20 })
      ]
    },
    accounts: {
      name: 'accounts',
      type: 'table',
      description: '账户表',
      fields: [
        createField('accounts.id', 'id', 'accounts', 'INT', { isPrimaryKey: true }),
        createField('accounts.customer_id', 'customer_id', 'accounts', 'INT', { isForeignKey: true }),
        createField('accounts.account_number', 'account_number', 'accounts', 'VARCHAR', { length: 20 }),
        createField('accounts.balance', 'balance', 'accounts', 'DECIMAL', { precision: 15, scale: 2 }),
        createField('accounts.account_type', 'account_type', 'accounts', 'VARCHAR', { length: 20 })
      ]
    },
    transactions: {
      name: 'transactions',
      type: 'table',
      description: '交易表',
      fields: [
        createField('transactions.id', 'id', 'transactions', 'INT', { isPrimaryKey: true }),
        createField('transactions.account_id', 'account_id', 'transactions', 'INT', { isForeignKey: true }),
        createField('transactions.amount', 'amount', 'transactions', 'DECIMAL', { precision: 12, scale: 2 }),
        createField('transactions.transaction_date', 'transaction_date', 'transactions', 'TIMESTAMP'),
        createField('transactions.transaction_type', 'transaction_type', 'transactions', 'VARCHAR', { length: 20 })
      ]
    },
    risk_assessment: {
      name: 'risk_assessment',
      type: 'view',
      description: '风险评估视图',
      fields: [
        createField('risk_assessment.customer_id', 'customer_id', 'risk_assessment', 'INT'),
        createField('risk_assessment.customer_name', 'customer_name', 'risk_assessment', 'VARCHAR', { length: 100 }),
        createField('risk_assessment.total_balance', 'total_balance', 'risk_assessment', 'DECIMAL', { precision: 15, scale: 2 }),
        createField('risk_assessment.transaction_count', 'transaction_count', 'risk_assessment', 'INT'),
        createField('risk_assessment.risk_score', 'risk_score', 'risk_assessment', 'DECIMAL', { precision: 5, scale: 2 })
      ]
    }
  }

  const nodes: LineageNode[] = Object.values(tables).flatMap(table => table.fields)

  const edges: LineageEdge[] = [
    createEdge('edge_1', 'customers.id', 'accounts.customer_id', TransformType.JOIN, '客户-账户关联', 1.0),
    createEdge('edge_2', 'accounts.id', 'transactions.account_id', TransformType.JOIN, '账户-交易关联', 1.0),
    createEdge('edge_3', 'customers.id', 'risk_assessment.customer_id', TransformType.DIRECT, '客户ID映射', 1.0),
    createEdge('edge_4', 'customers.name', 'risk_assessment.customer_name', TransformType.DIRECT, '客户名映射', 1.0),
    createEdge('edge_5', 'accounts.balance', 'risk_assessment.total_balance', TransformType.AGGREGATION, '余额汇总', 0.9),
    createEdge('edge_6', 'transactions.id', 'risk_assessment.transaction_count', TransformType.AGGREGATION, '交易计数', 0.9),
    createEdge('edge_7', 'customers.credit_score', 'risk_assessment.risk_score', TransformType.CALCULATION, '风险评分计算', 0.8)
  ]

  return {
    tables,
    nodes,
    edges,
    metadata: {
      version: '1.0.0',
      parseTime: new Date().toISOString(),
      sqlText: `
        SELECT
          c.id AS customer_id,
          c.name AS customer_name,
          SUM(a.balance) AS total_balance,
          COUNT(t.id) AS transaction_count,
          (c.credit_score * 0.6 + AVG(t.amount) * 0.4) AS risk_score
        FROM customers c
        JOIN accounts a ON c.id = a.customer_id
        LEFT JOIN transactions t ON a.id = t.account_id
        GROUP BY c.id, c.name, c.credit_score
      `
    }
  }
}

/**
 * 创建字段信息
 */
function createField(
  id: string,
  fieldName: string,
  tableName: string,
  dataType: string,
  options: {
    length?: number
    precision?: number
    scale?: number
    isPrimaryKey?: boolean
    isForeignKey?: boolean
    isNullable?: boolean
  } = {}
): FieldInfo {
  return {
    id,
    fieldName,
    tableName,
    type: 'field',
    dataType: {
      type: dataType,
      ...options
    },
    isPrimaryKey: options.isPrimaryKey || false,
    isForeignKey: options.isForeignKey || false,
    isNullable: options.isNullable !== false,
    label: fieldName
  }
}

/**
 * 创建边信息
 */
function createEdge(
  id: string,
  source: string,
  target: string,
  transformType: TransformType,
  label: string,
  confidence: number
): LineageEdge {
  return {
    id,
    source,
    target,
    transformType,
    label,
    confidence
  }
}

/**
 * 供应链场景示例数据
 */
function generateSupplyChainDemo(): LineageData {
  // 实现供应链场景数据生成
  return generateBasicDemo() // 临时返回基础数据
}

/**
 * 数据分析场景示例数据
 */
function generateAnalyticsDemo(): LineageData {
  // 实现数据分析场景数据生成
  return generateBasicDemo() // 临时返回基础数据
}

/**
 * 复杂查询场景示例数据
 */
function generateComplexDemo(): LineageData {
  // 实现复杂查询场景数据生成
  return generateBasicDemo() // 临时返回基础数据
}
