/**
 * G6示例数据测试
 * 测试新的G6风格示例数据生成功能
 */

import { createSampleLineageData } from '@/utils/sqlParser'
import { createG6SampleLineageData } from '@/utils/g6SampleData'
import { transformToG6Data } from '@/utils/graphDataTransform'

/**
 * 测试G6示例数据生成
 */
export function testG6SampleData() {
  console.log('\n🧪 开始测试G6示例数据生成...')
  
  try {
    // 1. 测试新的示例数据生成
    console.log('\n📊 生成G6风格示例数据...')
    const sampleData = createSampleLineageData()
    
    console.log('✅ 示例数据生成成功:', {
      tables: Object.keys(sampleData.tables).length,
      nodes: sampleData.nodes.length,
      edges: sampleData.edges.length,
      version: sampleData.metadata?.version
    })
    
    // 2. 检查combo信息
    console.log('\n🔍 检查combo信息...')
    const tablesWithCombo = Object.values(sampleData.tables).filter(table => table.combo)
    const comboTypes = [...new Set(tablesWithCombo.map(table => table.combo))]
    
    console.log('✅ Combo信息:', {
      tablesWithCombo: tablesWithCombo.length,
      comboTypes: comboTypes,
      comboDetails: tablesWithCombo.map(table => ({
        table: table.name,
        combo: table.combo,
        schema: table.schema
      }))
    })
    
    // 3. 转换为G6数据格式
    console.log('\n🔄 转换为G6数据格式...')
    const g6Data = transformToG6Data(sampleData)
    
    console.log('✅ G6数据转换成功:', {
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length,
      combos: g6Data.combos?.length || 0
    })
    
    // 4. 检查G6数据结构
    console.log('\n🔍 检查G6数据结构...')
    
    // 检查节点是否有comboId
    const nodesWithCombo = g6Data.nodes.filter(node => node.comboId)
    console.log('节点combo信息:', {
      totalNodes: g6Data.nodes.length,
      nodesWithCombo: nodesWithCombo.length,
      comboIds: [...new Set(nodesWithCombo.map(node => node.comboId))]
    })
    
    // 检查combo数据
    if (g6Data.combos && g6Data.combos.length > 0) {
      console.log('Combo数据:', g6Data.combos.map(combo => ({
        id: combo.id,
        label: combo.label,
        type: combo.type
      })))
    }
    
    // 5. 检查边的数据结构
    console.log('\n🔗 检查边的数据结构...')
    const edgeTypes = [...new Set(g6Data.edges.map(edge => edge.lineageEdge.transformType))]
    console.log('边类型统计:', {
      totalEdges: g6Data.edges.length,
      transformTypes: edgeTypes,
      edgeDetails: g6Data.edges.slice(0, 3).map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        transformType: edge.lineageEdge.transformType,
        label: edge.label
      }))
    })
    
    // 6. 验证数据完整性
    console.log('\n✅ 验证数据完整性...')
    const validation = validateG6Data(g6Data, sampleData)
    console.log('数据验证结果:', validation)
    
    console.log('\n🎉 G6示例数据测试完成！')
    return { success: true, data: g6Data, validation }
    
  } catch (error) {
    console.error('❌ G6示例数据测试失败:', error)
    return { success: false, error }
  }
}

/**
 * 验证G6数据的完整性
 */
function validateG6Data(g6Data: any, lineageData: any) {
  const issues: string[] = []
  const warnings: string[] = []
  
  // 检查节点数量
  const expectedNodeCount = lineageData.nodes.length
  if (g6Data.nodes.length !== expectedNodeCount) {
    issues.push(`节点数量不匹配: 期望 ${expectedNodeCount}, 实际 ${g6Data.nodes.length}`)
  }
  
  // 检查边数量
  const expectedEdgeCount = lineageData.edges.length
  if (g6Data.edges.length !== expectedEdgeCount) {
    issues.push(`边数量不匹配: 期望 ${expectedEdgeCount}, 实际 ${g6Data.edges.length}`)
  }
  
  // 检查combo数据
  const tablesWithCombo = Object.values(lineageData.tables).filter((table: any) => table.combo)
  const expectedComboCount = new Set(tablesWithCombo.map((table: any) => table.combo)).size
  const actualComboCount = g6Data.combos?.length || 0
  
  if (actualComboCount !== expectedComboCount) {
    issues.push(`Combo数量不匹配: 期望 ${expectedComboCount}, 实际 ${actualComboCount}`)
  }
  
  // 检查节点的comboId
  const nodesWithCombo = g6Data.nodes.filter((node: any) => node.comboId)
  if (nodesWithCombo.length !== tablesWithCombo.length) {
    warnings.push(`带combo的节点数量可能不正确: 期望 ${tablesWithCombo.length}, 实际 ${nodesWithCombo.length}`)
  }
  
  // 检查边的连接有效性
  const nodeIds = new Set(g6Data.nodes.map((node: any) => node.id))
  const invalidEdges = g6Data.edges.filter((edge: any) => 
    !nodeIds.has(edge.source) || !nodeIds.has(edge.target)
  )
  
  if (invalidEdges.length > 0) {
    issues.push(`发现 ${invalidEdges.length} 条无效边（源或目标节点不存在）`)
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    warnings,
    statistics: {
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length,
      combos: actualComboCount,
      nodesWithCombo: nodesWithCombo.length
    }
  }
}

/**
 * 运行测试（如果直接执行此文件）
 */
if (typeof window === 'undefined') {
  // Node.js环境下运行测试
  testG6SampleData()
}
