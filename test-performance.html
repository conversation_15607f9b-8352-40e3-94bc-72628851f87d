<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .status.loading {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .performance-info {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        
        .performance-metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .metric-label {
            font-weight: 500;
        }
        
        .metric-value {
            font-family: monospace;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数据血缘图性能测试</h1>
        <p>此页面用于测试异步数据处理是否解决了页面假死问题。</p>
        
        <div class="test-section">
            <h3>🧪 UI响应性测试</h3>
            <p>点击下面的按钮测试UI是否保持响应：</p>
            <button class="test-button" onclick="testUIResponsiveness()">测试UI响应性</button>
            <button class="test-button" onclick="simulateHeavyTask()">模拟重任务</button>
            <button class="test-button" onclick="simulateAsyncTask()">模拟异步任务</button>
            <div id="ui-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>⏱️ 性能监控</h3>
            <p>实时监控页面性能指标：</p>
            <button class="test-button" onclick="startPerformanceMonitoring()">开始监控</button>
            <button class="test-button" onclick="stopPerformanceMonitoring()">停止监控</button>
            <div class="performance-info">
                <div class="performance-metric">
                    <span class="metric-label">主线程阻塞时间:</span>
                    <span class="metric-value" id="blocking-time">0ms</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-label">内存使用:</span>
                    <span class="metric-value" id="memory-usage">0MB</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-label">FPS:</span>
                    <span class="metric-value" id="fps">0</span>
                </div>
                <div class="performance-metric">
                    <span class="metric-label">任务队列长度:</span>
                    <span class="metric-value" id="task-queue">0</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 血缘图测试</h3>
            <p>测试血缘图数据加载是否会阻塞UI：</p>
            <button class="test-button" onclick="openLineageApp()">打开血缘图应用</button>
            <button class="test-button" onclick="testDataLoading()">测试数据加载</button>
            <div id="lineage-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        let performanceMonitor = null;
        let fpsCounter = null;
        
        // UI响应性测试
        function testUIResponsiveness() {
            const status = document.getElementById('ui-status');
            status.style.display = 'block';
            status.className = 'status loading';
            status.textContent = '正在测试UI响应性...';
            
            let clickCount = 0;
            const startTime = Date.now();
            
            const testButton = document.createElement('button');
            testButton.textContent = '点击我测试响应性';
            testButton.className = 'test-button';
            testButton.onclick = () => {
                clickCount++;
                testButton.textContent = `已点击 ${clickCount} 次`;
            };
            
            status.appendChild(document.createElement('br'));
            status.appendChild(testButton);
            
            setTimeout(() => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                status.className = 'status success';
                status.innerHTML = `UI响应性测试完成<br>测试时长: ${duration}ms<br>点击次数: ${clickCount}<br>平均响应时间: ${clickCount > 0 ? (duration / clickCount).toFixed(2) : 0}ms`;
            }, 5000);
        }
        
        // 模拟重任务（同步）
        function simulateHeavyTask() {
            const status = document.getElementById('ui-status');
            status.style.display = 'block';
            status.className = 'status loading';
            status.textContent = '执行重任务中...（这会阻塞UI）';
            
            const startTime = Date.now();
            
            // 同步重任务 - 会阻塞UI
            setTimeout(() => {
                let result = 0;
                for (let i = 0; i < 100000000; i++) {
                    result += Math.random();
                }
                
                const endTime = Date.now();
                status.className = 'status error';
                status.textContent = `重任务完成，耗时: ${endTime - startTime}ms（UI被阻塞）`;
            }, 10);
        }
        
        // 模拟异步任务
        async function simulateAsyncTask() {
            const status = document.getElementById('ui-status');
            status.style.display = 'block';
            status.className = 'status loading';
            status.textContent = '执行异步任务中...（UI保持响应）';
            
            const startTime = Date.now();
            
            // 异步任务 - 不会阻塞UI
            await new Promise(resolve => {
                let completed = 0;
                const total = 10;
                
                function processChunk() {
                    let result = 0;
                    for (let i = 0; i < 10000000; i++) {
                        result += Math.random();
                    }
                    completed++;
                    
                    status.textContent = `异步任务进度: ${completed}/${total} (${(completed/total*100).toFixed(0)}%)`;
                    
                    if (completed < total) {
                        setTimeout(processChunk, 10);
                    } else {
                        resolve();
                    }
                }
                
                processChunk();
            });
            
            const endTime = Date.now();
            status.className = 'status success';
            status.textContent = `异步任务完成，耗时: ${endTime - startTime}ms（UI保持响应）`;
        }
        
        // 性能监控
        function startPerformanceMonitoring() {
            if (performanceMonitor) return;
            
            performanceMonitor = setInterval(() => {
                // 内存使用
                if ('memory' in performance) {
                    const memory = performance.memory;
                    document.getElementById('memory-usage').textContent = 
                        Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB';
                }
                
                // 任务队列长度（模拟）
                document.getElementById('task-queue').textContent = 
                    Math.floor(Math.random() * 10);
            }, 1000);
            
            // FPS监控
            let frames = 0;
            let lastTime = Date.now();
            
            function countFPS() {
                frames++;
                const now = Date.now();
                if (now - lastTime >= 1000) {
                    document.getElementById('fps').textContent = frames;
                    frames = 0;
                    lastTime = now;
                }
                fpsCounter = requestAnimationFrame(countFPS);
            }
            countFPS();
        }
        
        function stopPerformanceMonitoring() {
            if (performanceMonitor) {
                clearInterval(performanceMonitor);
                performanceMonitor = null;
            }
            if (fpsCounter) {
                cancelAnimationFrame(fpsCounter);
                fpsCounter = null;
            }
        }
        
        // 打开血缘图应用
        function openLineageApp() {
            window.open('http://localhost:5180/', '_blank');
        }
        
        // 测试数据加载
        function testDataLoading() {
            const status = document.getElementById('lineage-status');
            status.style.display = 'block';
            status.className = 'status loading';
            status.textContent = '请在血缘图应用中点击"加载示例数据"按钮，然后观察此页面是否保持响应...';
            
            let testCount = 0;
            const testInterval = setInterval(() => {
                testCount++;
                status.textContent = `测试进行中... ${testCount}s（如果能看到这个计数器更新，说明UI没有被阻塞）`;
            }, 1000);
            
            setTimeout(() => {
                clearInterval(testInterval);
                status.className = 'status success';
                status.textContent = '测试完成！如果计数器正常更新，说明异步处理生效了。';
            }, 30000);
        }
        
        // 页面加载时开始性能监控
        window.addEventListener('load', () => {
            startPerformanceMonitoring();
        });
        
        // 页面卸载时停止监控
        window.addEventListener('beforeunload', () => {
            stopPerformanceMonitoring();
        });
    </script>
</body>
</html>
