<template>
  <div class="advanced-interaction-test">
    <a-card title="高级交互功能测试" style="margin: 20px;">
      <a-space direction="vertical" style="width: 100%;">

        <!-- 搜索功能测试 -->
        <a-card size="small" title="搜索定位功能测试">
          <a-space>
            <a-input
              v-model:value="searchKeyword"
              placeholder="输入表名或字段名进行搜索..."
              style="width: 300px;"
              @pressEnter="handleSearch"
            />
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="clearSearch">清空</a-button>
          </a-space>

          <div v-if="searchResults.length > 0" style="margin-top: 10px;">
            <a-divider orientation="left" plain>搜索结果</a-divider>
            <a-list size="small" :data-source="searchResults">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-space>
                    <a-tag :color="item.type === 'table' ? 'blue' : 'green'">
                      {{ item.type === 'table' ? '表' : '字段' }}
                    </a-tag>
                    <span>{{ item.name }}</span>
                    <span v-if="item.tableName" style="color: #666;">
                      ({{ item.tableName }})
                    </span>
                    <a-button size="small" @click="locateToItem(item)">
                      定位
                    </a-button>
                  </a-space>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>

        <!-- 导出功能测试 -->
        <a-card size="small" title="导出功能测试">
          <a-space>
            <a-button type="primary" @click="exportPNG">导出PNG</a-button>
            <a-button @click="exportJPEG">导出JPEG</a-button>
            <a-button @click="exportPDF">导出PDF</a-button>
          </a-space>
        </a-card>

        <!-- MiniMap功能测试 -->
        <a-card size="small" title="缩略图功能测试">
          <a-space>
            <a-button @click="toggleMiniMap">切换缩略图显示</a-button>
            <a-tag :color="minimapVisible ? 'green' : 'red'">
              缩略图状态: {{ minimapVisible ? '显示' : '隐藏' }}
            </a-tag>
          </a-space>
        </a-card>

        <!-- 布局功能测试 -->
        <a-card size="small" title="布局功能测试">
          <a-space>
            <a-button @click="resetLayout">重置布局</a-button>
            <a-button @click="fitView">适应视图</a-button>
            <a-button @click="resetView">重置视图</a-button>
          </a-space>
        </a-card>

        <!-- 测试结果显示 -->
        <a-card size="small" title="测试结果">
          <a-textarea
            v-model:value="testResults"
            :rows="8"
            readonly
            placeholder="测试结果将在这里显示..."
          />
          <div style="margin-top: 10px;">
            <a-button @click="clearResults">清空结果</a-button>
            <a-button @click="runAllTests" type="primary">运行所有测试</a-button>
          </div>
        </a-card>

      </a-space>
    </a-card>

    <!-- 图谱组件 -->
    <div style="margin: 20px; height: 600px; border: 1px solid #d9d9d9;">
      <LineageGraph
        ref="graphRef"
        :data="testData"
        :width="800"
        :height="600"
        @graph-ready="handleGraphReady"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import { useLineageStore } from '@/stores/lineageStore'
import type { G6GraphData } from '@/types/lineage'

// 响应式数据
const searchKeyword = ref('')
const searchResults = ref<any[]>([])
const minimapVisible = ref(true)
const testResults = ref('')
const graphRef = ref<any>(null)

// 使用store
const lineageStore = useLineageStore()

// 测试数据
const testData = ref<G6GraphData>({
  nodes: [
    {
      id: 'user_table',
      tableName: 'user_table',
      fields: [
        {
          id: 'user_table.user_id',
          label: 'user_id',
          tableName: 'user_table',
          fieldName: 'user_id',
          dataType: { type: 'bigint' },
          description: '用户ID',
          type: 'field',
          isKey: true
        },
        {
          id: 'user_table.username',
          label: 'username',
          tableName: 'user_table',
          fieldName: 'username',
          dataType: { type: 'varchar', length: 50 },
          description: '用户名',
          type: 'field'
        }
      ],
      tableInfo: {
        name: 'user_table',
        type: 'table',
        fields: [],
        description: '用户表',
        rowCount: 10000
      }
    },
    {
      id: 'order_table',
      tableName: 'order_table',
      fields: [
        {
          id: 'order_table.order_id',
          label: 'order_id',
          tableName: 'order_table',
          fieldName: 'order_id',
          dataType: { type: 'bigint' },
          description: '订单ID',
          type: 'field',
          isKey: true
        },
        {
          id: 'order_table.user_id',
          label: 'user_id',
          tableName: 'order_table',
          fieldName: 'user_id',
          dataType: { type: 'bigint' },
          description: '用户ID外键',
          type: 'field'
        }
      ],
      tableInfo: {
        name: 'order_table',
        type: 'table',
        fields: [],
        description: '订单表',
        rowCount: 50000
      }
    }
  ],
  edges: [
    {
      id: 'edge1',
      source: 'user_table.user_id',
      target: 'order_table.user_id',
      lineageEdge: {
        id: 'edge1',
        source: 'user_table.user_id',
        target: 'order_table.user_id',
        transformType: 'DIRECT',
        confidence: 0.95,
        label: '用户ID关联'
      }
    }
  ]
})

// 方法
const addTestResult = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  testResults.value += `[${timestamp}] ${message}\n`
}

const handleGraphReady = (graph: any) => {
  addTestResult('图谱初始化完成')
  console.log('Graph ready:', graph)
}

const handleSearch = () => {
  if (!graphRef.value || !searchKeyword.value.trim()) {
    message.warning('请输入搜索关键词')
    return
  }

  try {
    const results = graphRef.value.searchAndLocate(searchKeyword.value)
    searchResults.value = results
    addTestResult(`搜索"${searchKeyword.value}"找到${results.length}个结果`)
  } catch (error) {
    addTestResult(`搜索失败: ${error}`)
    message.error('搜索失败')
  }
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  addTestResult('清空搜索结果')
}

const locateToItem = (item: any) => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    const success = graphRef.value.locateToNode(item.id, true)
    if (success) {
      addTestResult(`成功定位到${item.type}: ${item.name}`)
      message.success('定位成功')
    } else {
      addTestResult(`定位失败: ${item.name}`)
      message.error('定位失败')
    }
  } catch (error) {
    addTestResult(`定位异常: ${error}`)
    message.error('定位异常')
  }
}

const exportPNG = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    const result = graphRef.value.exportAsImage('png', 'test-graph.png')
    if (result) {
      addTestResult('PNG导出成功')
      message.success('PNG导出成功')
    } else {
      addTestResult('PNG导出失败')
      message.error('PNG导出失败')
    }
  } catch (error) {
    addTestResult(`PNG导出异常: ${error}`)
    message.error('PNG导出异常')
  }
}

const exportJPEG = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    const result = graphRef.value.exportAsImage('jpeg', 'test-graph.jpg')
    if (result) {
      addTestResult('JPEG导出成功')
      message.success('JPEG导出成功')
    } else {
      addTestResult('JPEG导出失败')
      message.error('JPEG导出失败')
    }
  } catch (error) {
    addTestResult(`JPEG导出异常: ${error}`)
    message.error('JPEG导出异常')
  }
}

const exportPDF = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    const result = graphRef.value.exportAsPDF('test-graph.pdf')
    if (result) {
      addTestResult('PDF导出成功')
      message.success('PDF导出成功')
    } else {
      addTestResult('PDF导出失败')
      message.error('PDF导出失败')
    }
  } catch (error) {
    addTestResult(`PDF导出异常: ${error}`)
    message.error('PDF导出异常')
  }
}

const toggleMiniMap = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    graphRef.value.toggleMiniMap()
    minimapVisible.value = graphRef.value.showMiniMap
    addTestResult(`缩略图已${minimapVisible.value ? '显示' : '隐藏'}`)
    message.success(`缩略图已${minimapVisible.value ? '显示' : '隐藏'}`)
  } catch (error) {
    addTestResult(`切换缩略图异常: ${error}`)
    message.error('切换缩略图异常')
  }
}

const resetLayout = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    const success = graphRef.value.resetLayout()
    if (success) {
      addTestResult('重置布局成功')
      message.success('重置布局成功')
    } else {
      addTestResult('重置布局失败')
      message.error('重置布局失败')
    }
  } catch (error) {
    addTestResult(`重置布局异常: ${error}`)
    message.error('重置布局异常')
  }
}

const fitView = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    graphRef.value.fitView()
    addTestResult('适应视图成功')
    message.success('适应视图成功')
  } catch (error) {
    addTestResult(`适应视图异常: ${error}`)
    message.error('适应视图异常')
  }
}

const resetView = () => {
  if (!graphRef.value) {
    message.warning('图谱未就绪')
    return
  }

  try {
    graphRef.value.resetView()
    addTestResult('重置视图成功')
    message.success('重置视图成功')
  } catch (error) {
    addTestResult(`重置视图异常: ${error}`)
    message.error('重置视图异常')
  }
}

const clearResults = () => {
  testResults.value = ''
  addTestResult('测试结果已清空')
}

const runAllTests = async () => {
  addTestResult('开始运行所有测试...')

  // 等待图谱就绪
  if (!graphRef.value || !graphRef.value.isReady) {
    addTestResult('等待图谱初始化...')
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  // 测试搜索功能
  searchKeyword.value = 'user'
  handleSearch()

  await new Promise(resolve => setTimeout(resolve, 500))

  // 测试定位功能
  if (searchResults.value.length > 0) {
    locateToItem(searchResults.value[0])
  }

  await new Promise(resolve => setTimeout(resolve, 500))

  // 测试MiniMap
  toggleMiniMap()

  await new Promise(resolve => setTimeout(resolve, 500))

  // 测试布局功能
  resetLayout()

  await new Promise(resolve => setTimeout(resolve, 500))

  fitView()

  addTestResult('所有测试完成!')
  message.success('所有测试完成!')
}

onMounted(() => {
  addTestResult('高级交互功能测试页面已加载')
})
</script>

<style scoped>
.advanced-interaction-test {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
</style>
