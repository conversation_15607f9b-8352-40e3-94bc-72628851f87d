<template>
  <div class="data-transform-test">
    <a-card title="数据转换工具测试" style="margin-bottom: 16px;">
      <a-space direction="vertical" style="width: 100%;">
        <a-button-group>
          <a-button type="primary" @click="testBasicTransform">基础数据转换测试</a-button>
          <a-button @click="testRawDataTransform">原始JSON转换测试</a-button>
          <a-button @click="testSqlParsing">SQL解析测试</a-button>
          <a-button @click="testDataValidation">数据验证测试</a-button>
          <a-button @click="testLayoutOptimization">布局优化测试</a-button>
        </a-button-group>
        
        <a-button @click="clearResults" type="default">清空结果</a-button>
      </a-space>
    </a-card>

    <!-- 测试结果显示 -->
    <a-card title="测试结果" v-if="testResults.length > 0">
      <div v-for="(result, index) in testResults" :key="index" class="test-result">
        <a-alert
          :type="result.success ? 'success' : 'error'"
          :message="result.title"
          :description="result.description"
          show-icon
          style="margin-bottom: 8px;"
        />
        
        <a-collapse v-if="result.details" style="margin-bottom: 16px;">
          <a-collapse-panel key="1" header="详细信息">
            <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-card>

    <!-- 数据统计信息 -->
    <a-card title="数据统计" v-if="statistics">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="表数量">{{ statistics.tableCount }}</a-descriptions-item>
        <a-descriptions-item label="字段数量">{{ statistics.fieldCount }}</a-descriptions-item>
        <a-descriptions-item label="边数量">{{ statistics.edgeCount }}</a-descriptions-item>
        <a-descriptions-item label="最大字段数/表">{{ statistics.maxFieldsPerTable }}</a-descriptions-item>
        <a-descriptions-item label="平均字段数/表">{{ statistics.avgFieldsPerTable }}</a-descriptions-item>
        <a-descriptions-item label="转换类型">
          <a-tag v-for="(count, type) in statistics.transformTypes" :key="type" style="margin: 2px;">
            {{ type }}: {{ count }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>
      
      <a-divider>置信度分布</a-divider>
      <a-progress 
        :percent="Math.round((statistics.confidenceDistribution.high / (statistics.confidenceDistribution.high + statistics.confidenceDistribution.medium + statistics.confidenceDistribution.low)) * 100)" 
        status="success" 
        :format="() => `高置信度: ${statistics.confidenceDistribution.high}`"
      />
      <a-progress 
        :percent="Math.round((statistics.confidenceDistribution.medium / (statistics.confidenceDistribution.high + statistics.confidenceDistribution.medium + statistics.confidenceDistribution.low)) * 100)" 
        status="normal" 
        :format="() => `中等置信度: ${statistics.confidenceDistribution.medium}`"
      />
      <a-progress 
        :percent="Math.round((statistics.confidenceDistribution.low / (statistics.confidenceDistribution.high + statistics.confidenceDistribution.medium + statistics.confidenceDistribution.low)) * 100)" 
        status="exception" 
        :format="() => `低置信度: ${statistics.confidenceDistribution.low}`"
      />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  transformToG6Data, 
  transformRawDataToLineageData,
  validateLineageData,
  optimizeGraphLayout,
  getDataStatistics
} from '@/utils/graphDataTransform'
import { 
  createSampleLineageData, 
  parseSqlToLineageData,
  formatSql,
  extractTableNames,
  extractFieldNames
} from '@/utils/sqlParser'
import type { LineageData } from '@/types/lineage'

// 响应式数据
const testResults = ref<Array<{
  title: string;
  description: string;
  success: boolean;
  details?: any;
}>>([])

const statistics = ref<any>(null)

// 测试函数
const testBasicTransform = () => {
  try {
    addResult('开始基础数据转换测试', '测试从血缘数据到G6数据的转换', true)
    
    // 创建示例数据
    const lineageData = createSampleLineageData()
    addResult('✅ 创建示例数据', `成功创建 ${Object.keys(lineageData.tables).length} 个表`, true, {
      tableCount: Object.keys(lineageData.tables).length,
      nodeCount: lineageData.nodes.length,
      edgeCount: lineageData.edges.length
    })
    
    // 转换为G6数据
    const g6Data = transformToG6Data(lineageData)
    addResult('✅ G6数据转换', `成功转换 ${g6Data.nodes.length} 个节点和 ${g6Data.edges.length} 条边`, true, {
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length,
      sampleNode: g6Data.nodes[0],
      sampleEdge: g6Data.edges[0]
    })
    
    // 更新统计信息
    statistics.value = getDataStatistics(lineageData)
    
  } catch (error) {
    addResult('❌ 基础转换测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

const testRawDataTransform = () => {
  try {
    addResult('开始原始JSON转换测试', '测试从各种格式的原始数据转换为标准血缘数据', true)
    
    // 测试数组格式
    const arrayData = [
      {
        name: 'test_table1',
        fields: [
          { name: 'id', type: 'INT', isPrimaryKey: true },
          { name: 'name', type: 'VARCHAR' }
        ]
      },
      {
        name: 'test_table2', 
        fields: [
          { name: 'id', type: 'INT' },
          { name: 'user_id', type: 'INT' }
        ]
      }
    ]
    
    const transformedArrayData = transformRawDataToLineageData(arrayData)
    addResult('✅ 数组格式转换', `成功转换数组格式数据`, true, {
      tableCount: Object.keys(transformedArrayData.tables).length,
      nodeCount: transformedArrayData.nodes.length
    })
    
    // 测试对象格式
    const objectData = {
      tables: {
        users: {
          fields: [
            { name: 'id', type: 'INT' },
            { name: 'email', type: 'VARCHAR' }
          ]
        }
      },
      relationships: [
        {
          source: 'users.id',
          target: 'orders.user_id',
          type: 'JOIN'
        }
      ]
    }
    
    const transformedObjectData = transformRawDataToLineageData(objectData)
    addResult('✅ 对象格式转换', `成功转换对象格式数据`, true, {
      tableCount: Object.keys(transformedObjectData.tables).length,
      edgeCount: transformedObjectData.edges.length
    })
    
  } catch (error) {
    addResult('❌ 原始数据转换测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

const testSqlParsing = () => {
  try {
    addResult('开始SQL解析测试', '测试SQL语句解析和血缘关系提取', true)
    
    const testSql = `
      SELECT 
        u.id as user_id,
        u.name as user_name,
        o.amount,
        COUNT(o.id) as order_count
      FROM users u
      LEFT JOIN orders o ON u.id = o.user_id
      WHERE u.status = 'active'
      GROUP BY u.id, u.name
    `
    
    // 测试SQL格式化
    const formattedSql = formatSql(testSql)
    addResult('✅ SQL格式化', '成功格式化SQL语句', true, { formattedSql })
    
    // 测试表名提取
    const tableNames = extractTableNames(testSql)
    addResult('✅ 表名提取', `提取到 ${tableNames.length} 个表名`, true, { tableNames })
    
    // 测试字段名提取
    const fieldNames = extractFieldNames(testSql)
    addResult('✅ 字段名提取', `提取到 ${fieldNames.length} 个字段名`, true, { fieldNames })
    
    // 测试完整SQL解析
    const parsedData = parseSqlToLineageData(testSql)
    addResult('✅ 完整SQL解析', `解析生成 ${Object.keys(parsedData.tables).length} 个表`, true, {
      tableCount: Object.keys(parsedData.tables).length,
      nodeCount: parsedData.nodes.length,
      edgeCount: parsedData.edges.length
    })
    
  } catch (error) {
    addResult('❌ SQL解析测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

const testDataValidation = () => {
  try {
    addResult('开始数据验证测试', '测试血缘数据的完整性验证', true)
    
    // 测试有效数据
    const validData = createSampleLineageData()
    const validationResult = validateLineageData(validData)
    addResult('✅ 有效数据验证', `验证结果: ${validationResult.isValid ? '通过' : '失败'}`, validationResult.isValid, {
      isValid: validationResult.isValid,
      errors: validationResult.errors,
      warnings: validationResult.warnings
    })
    
    // 测试无效数据
    const invalidData: any = {
      tables: {},
      nodes: [],
      edges: [
        { id: 'edge1', source: 'nonexistent.field', target: 'another.field' }
      ]
    }
    
    const invalidValidationResult = validateLineageData(invalidData)
    addResult('✅ 无效数据验证', `验证结果: ${invalidValidationResult.isValid ? '通过' : '失败'}`, !invalidValidationResult.isValid, {
      isValid: invalidValidationResult.isValid,
      errors: invalidValidationResult.errors,
      warnings: invalidValidationResult.warnings
    })
    
  } catch (error) {
    addResult('❌ 数据验证测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

const testLayoutOptimization = () => {
  try {
    addResult('开始布局优化测试', '测试图数据布局优化功能', true)
    
    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)
    
    // 测试不同方向的布局优化
    const directions = ['LR', 'TB', 'RL', 'BT'] as const
    
    directions.forEach(direction => {
      const optimizedData = optimizeGraphLayout(g6Data, {
        direction,
        nodeSpacing: 100,
        rankSpacing: 200
      })
      
      const hasPositions = optimizedData.nodes.every(node => 
        typeof node.x === 'number' && typeof node.y === 'number'
      )
      
      addResult(`✅ ${direction}方向布局`, `布局优化${hasPositions ? '成功' : '失败'}`, hasPositions, {
        direction,
        nodeCount: optimizedData.nodes.length,
        hasPositions
      })
    })
    
  } catch (error) {
    addResult('❌ 布局优化测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

const addResult = (title: string, description: string, success: boolean, details?: any) => {
  testResults.value.push({
    title,
    description,
    success,
    details
  })
}

const clearResults = () => {
  testResults.value = []
  statistics.value = null
}
</script>

<style scoped>
.data-transform-test {
  padding: 16px;
}

.test-result {
  margin-bottom: 16px;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
}
</style>
