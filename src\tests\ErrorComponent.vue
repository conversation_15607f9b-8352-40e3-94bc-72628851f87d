<template>
  <div class="error-component">
    <h3>测试错误组件</h3>
    <p>这个组件用于测试错误边界功能</p>
    
    <div v-if="shouldError">
      <!-- 这里会故意触发一个错误 -->
      {{ triggerError() }}
    </div>
    
    <div v-else>
      <a-result
        status="success"
        title="组件正常运行"
        sub-title="当前组件没有错误"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  shouldError?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  shouldError: false
})

// 故意触发错误的函数
const triggerError = () => {
  if (props.shouldError) {
    // 故意访问一个不存在的属性来触发错误
    const obj: any = null
    return obj.nonExistentProperty.anotherProperty
  }
  return '正常内容'
}

// 计算属性也可能触发错误
const computedValue = computed(() => {
  if (props.shouldError) {
    throw new Error('这是一个计算属性中的测试错误')
  }
  return '计算属性正常值'
})
</script>

<style scoped>
.error-component {
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}
</style>
