/**
 * 图数据转换工具
 * 用于将原始血缘数据转换为G6可用的节点和边数据
 */

import type {
  LineageData,
  G6GraphData,
  G6NodeData,
  G6EdgeData,
  TableInfo,
  LineageNode,
  LineageEdge
} from '@/types/lineage'
import dagre from 'dagre'

/**
 * 将血缘数据转换为G6图数据
 * @param lineageData 原始血缘数据
 * @returns G6图数据
 */
export function transformToG6Data(lineageData: LineageData): G6GraphData {
  const nodes: G6NodeData[] = []
  const edges: G6EdgeData[] = []
  const combos: any[] = []
  const comboSet = new Set<string>()

  // 转换表节点
  Object.values(lineageData.tables).forEach((table: TableInfo) => {
    const nodeData: G6NodeData = {
      id: table.name,
      type: 'rect', // 使用G6内置的rect类型，类似示例代码
      x: table.position?.x,
      y: table.position?.y,
      tableName: table.name,
      fields: table.fields,
      tableInfo: table,
      label: table.name,
      labelCfg: {
        position: 'top',
        offset: 10
      }
    }

    // 如果表有combo属性，设置comboId并收集combo信息
    if (table.combo) {
      nodeData.comboId = table.combo
      comboSet.add(table.combo)
    }

    nodes.push(nodeData)
  })

  // 创建combo数据
  comboSet.forEach(comboId => {
    combos.push({
      id: comboId,
      label: getComboLabel(comboId),
      type: 'rect',
      style: {
        radius: 8,
        fill: getComboColor(comboId),
        stroke: getComboStrokeColor(comboId),
        lineWidth: 2,
        opacity: 0.1
      }
    })
  })

  // 转换边数据 - 支持字段级连接
  lineageData.edges.forEach((edge) => {
    const sourceTableName = getTableNameFromFieldId(edge.source)
    const targetTableName = getTableNameFromFieldId(edge.target)
    const sourceFieldName = getFieldNameFromFieldId(edge.source)
    const targetFieldName = getFieldNameFromFieldId(edge.target)

    const edgeData: G6EdgeData = {
      id: edge.id,
      source: sourceTableName,
      target: targetTableName,
      type: 'field-edge',
      lineageEdge: edge,
      label: edge.label,
      // 添加字段级连接的额外信息
      sourceField: sourceFieldName,
      targetField: targetFieldName,
      sourceFieldId: edge.source,
      targetFieldId: edge.target,
      transformType: edge.transformType,
      confidence: edge.confidence,
      expression: edge.expression,
      style: {
        // 根据转换类型设置不同的样式
        stroke: getEdgeColorByTransformType(edge.transformType),
        lineWidth: getEdgeWidthByConfidence(edge.confidence),
        lineDash: getEdgeDashByTransformType(edge.transformType)
      },
      labelCfg: {
        autoRotate: true,
        style: {
          fontSize: 10,
          fill: '#666',
          background: {
            fill: '#ffffff',
            padding: [2, 4],
            radius: 2
          }
        }
      }
    }
    edges.push(edgeData)
  })

  return { nodes, edges, combos }
}

/**
 * 异步将血缘数据转换为G6图数据（用于大数据量）
 * @param lineageData 原始血缘数据
 * @returns Promise<G6图数据>
 */
export async function transformToG6DataAsync(lineageData: LineageData): Promise<G6GraphData> {
  return new Promise((resolve) => {
    // 使用 setTimeout 将计算移到下一个事件循环
    setTimeout(() => {
      try {
        const result = transformToG6Data(lineageData)
        resolve(result)
      } catch (error) {
        console.error('异步数据转换失败:', error)
        // 返回空数据而不是抛出错误
        resolve({ nodes: [], edges: [], combos: [] })
      }
    }, 0)
  })
}

/**
 * 从字段ID中提取表名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 表名
 */
export function getTableNameFromFieldId(fieldId: string): string {
  return fieldId.split('.')[0]
}

/**
 * 从字段ID中提取字段名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 字段名
 */
export function getFieldNameFromFieldId(fieldId: string): string {
  return fieldId.split('.').slice(1).join('.')
}

/**
 * 生成字段ID
 * @param tableName 表名
 * @param fieldName 字段名
 * @returns 字段ID
 */
export function generateFieldId(tableName: string, fieldName: string): string {
  return `${tableName}.${fieldName}`
}

/**
 * 计算表节点的尺寸
 * @param fields 字段列表
 * @returns 节点尺寸 [width, height]
 */
export function calculateNodeSize(fields: LineageNode[]): [number, number] {
  const minWidth = 200
  const minHeight = 100
  const fieldHeight = 24
  const headerHeight = 40
  const padding = 16

  // 计算最长字段名的宽度（简单估算）
  const maxFieldNameLength = Math.max(
    ...fields.map(field => field.fieldName.length)
  )
  const estimatedWidth = Math.max(minWidth, maxFieldNameLength * 8 + padding * 2)

  // 计算高度
  const estimatedHeight = Math.max(
    minHeight,
    headerHeight + fields.length * fieldHeight + padding
  )

  return [estimatedWidth, estimatedHeight]
}

/**
 * 根据字段类型获取颜色
 * @param dataType 数据类型
 * @returns 颜色值
 */
export function getFieldTypeColor(dataType?: string): string {
  if (!dataType) return '#666'

  const type = dataType.toLowerCase()

  if (type.includes('int') || type.includes('number') || type.includes('decimal')) {
    return '#1890ff' // 蓝色 - 数值类型
  }
  if (type.includes('varchar') || type.includes('char') || type.includes('text')) {
    return '#52c41a' // 绿色 - 字符类型
  }
  if (type.includes('date') || type.includes('time') || type.includes('timestamp')) {
    return '#fa8c16' // 橙色 - 时间类型
  }
  if (type.includes('bool') || type.includes('bit')) {
    return '#eb2f96' // 粉色 - 布尔类型
  }

  return '#666' // 默认灰色
}

/**
 * 格式化数据类型显示
 * @param dataType 数据类型对象
 * @returns 格式化后的类型字符串
 */
export function formatDataType(dataType: any): string {
  if (!dataType) return 'unknown'

  if (typeof dataType === 'string') {
    return dataType
  }

  let result = dataType.type || 'unknown'

  if (dataType.length) {
    result += `(${dataType.length}`
    if (dataType.precision && dataType.scale) {
      result += `,${dataType.scale}`
    }
    result += ')'
  } else if (dataType.precision) {
    result += `(${dataType.precision}`
    if (dataType.scale) {
      result += `,${dataType.scale}`
    }
    result += ')'
  }

  return result
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 数据验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: string[];
}

/**
 * 验证错误接口
 */
export interface ValidationError {
  code: string;
  message: string;
  field?: string;
  severity: 'error' | 'warning' | 'info';
}

/**
 * 验证警告接口
 */
export interface ValidationWarning {
  code: string;
  message: string;
  field?: string;
  suggestion?: string;
}

/**
 * 验证血缘数据的完整性（增强版）
 * @param lineageData 血缘数据
 * @returns 验证结果
 */
export function validateLineageData(lineageData: any): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []
  const suggestions: string[] = []

  // 检查基本结构
  if (!lineageData) {
    errors.push({
      code: 'NULL_DATA',
      message: '血缘数据不能为空',
      severity: 'error'
    })
    return { isValid: false, errors, warnings, suggestions }
  }

  // 检查数据类型
  if (typeof lineageData !== 'object') {
    errors.push({
      code: 'INVALID_TYPE',
      message: '血缘数据必须是对象类型',
      severity: 'error'
    })
    return { isValid: false, errors, warnings, suggestions }
  }

  // 检查必需字段
  const requiredFields = ['tables', 'nodes', 'edges']
  requiredFields.forEach(field => {
    if (!(field in lineageData)) {
      errors.push({
        code: 'MISSING_FIELD',
        message: `缺少必需字段: ${field}`,
        field,
        severity: 'error'
      })
    }
  })

  // 如果缺少基本字段，返回错误
  if (errors.length > 0) {
    return { isValid: false, errors, warnings, suggestions }
  }

  // 检查表数据
  if (!lineageData.tables || typeof lineageData.tables !== 'object') {
    errors.push({
      code: 'INVALID_TABLES',
      message: 'tables字段必须是对象类型',
      field: 'tables',
      severity: 'error'
    })
  } else if (Object.keys(lineageData.tables).length === 0) {
    warnings.push({
      code: 'EMPTY_TABLES',
      message: '没有表数据',
      field: 'tables',
      suggestion: '至少需要一个表才能生成血缘图'
    })
    suggestions.push('请确保数据中包含至少一个表的信息')
  }

  // 检查节点数据
  if (!Array.isArray(lineageData.nodes)) {
    errors.push({
      code: 'INVALID_NODES',
      message: 'nodes字段必须是数组类型',
      field: 'nodes',
      severity: 'error'
    })
  } else if (lineageData.nodes.length === 0) {
    warnings.push({
      code: 'EMPTY_NODES',
      message: '没有字段节点数据',
      field: 'nodes',
      suggestion: '字段节点用于显示表中的具体字段'
    })
  }

  // 检查边数据
  if (!Array.isArray(lineageData.edges)) {
    errors.push({
      code: 'INVALID_EDGES',
      message: 'edges字段必须是数组类型',
      field: 'edges',
      severity: 'error'
    })
  } else if (lineageData.edges.length === 0) {
    warnings.push({
      code: 'EMPTY_EDGES',
      message: '没有血缘关系边数据',
      field: 'edges',
      suggestion: '边数据用于显示字段之间的血缘关系'
    })
  }

  // 如果有严重错误，直接返回
  if (errors.length > 0) {
    return { isValid: false, errors, warnings, suggestions }
  }

  // 详细检查表数据
  validateTables(lineageData.tables, errors, warnings, suggestions)

  // 详细检查节点数据
  validateNodes(lineageData.nodes, errors, warnings, suggestions)

  // 详细检查边数据
  validateEdges(lineageData.edges, lineageData.nodes, errors, warnings, suggestions)

  // 检查数据一致性
  validateDataConsistency(lineageData, errors, warnings, suggestions)

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions
  }
}

/**
 * 验证表数据
 */
function validateTables(
  tables: any,
  errors: ValidationError[],
  warnings: ValidationWarning[],
  suggestions: string[]
) {
  Object.entries(tables).forEach(([tableName, table]: [string, any]) => {
    if (!table || typeof table !== 'object') {
      errors.push({
        code: 'INVALID_TABLE',
        message: `表 ${tableName} 数据格式错误`,
        field: `tables.${tableName}`,
        severity: 'error'
      })
      return
    }

    // 检查表名
    if (!table.name) {
      errors.push({
        code: 'MISSING_TABLE_NAME',
        message: `表 ${tableName} 缺少name字段`,
        field: `tables.${tableName}.name`,
        severity: 'error'
      })
    } else if (table.name !== tableName) {
      warnings.push({
        code: 'TABLE_NAME_MISMATCH',
        message: `表键名 ${tableName} 与表名 ${table.name} 不匹配`,
        field: `tables.${tableName}.name`,
        suggestion: '建议保持表键名与表名一致'
      })
    }

    // 检查字段数组
    if (!table.fields) {
      warnings.push({
        code: 'MISSING_FIELDS',
        message: `表 ${table.name || tableName} 缺少fields字段`,
        field: `tables.${tableName}.fields`,
        suggestion: '表应该包含字段信息'
      })
    } else if (!Array.isArray(table.fields)) {
      errors.push({
        code: 'INVALID_FIELDS_TYPE',
        message: `表 ${table.name || tableName} 的fields必须是数组`,
        field: `tables.${tableName}.fields`,
        severity: 'error'
      })
    } else if (table.fields.length === 0) {
      warnings.push({
        code: 'EMPTY_FIELDS',
        message: `表 ${table.name || tableName} 没有字段`,
        field: `tables.${tableName}.fields`,
        suggestion: '表应该至少包含一个字段'
      })
    }
  })
}

/**
 * 验证节点数据
 */
function validateNodes(
  nodes: any[],
  errors: ValidationError[],
  warnings: ValidationWarning[],
  suggestions: string[]
) {
  const nodeIds = new Set<string>()

  nodes.forEach((node, index) => {
    if (!node || typeof node !== 'object') {
      errors.push({
        code: 'INVALID_NODE',
        message: `节点 ${index} 数据格式错误`,
        field: `nodes[${index}]`,
        severity: 'error'
      })
      return
    }

    // 检查必需字段
    const requiredNodeFields = ['id', 'fieldName', 'tableName']
    requiredNodeFields.forEach(field => {
      if (!node[field]) {
        errors.push({
          code: 'MISSING_NODE_FIELD',
          message: `节点 ${index} 缺少必需字段: ${field}`,
          field: `nodes[${index}].${field}`,
          severity: 'error'
        })
      }
    })

    // 检查ID唯一性
    if (node.id) {
      if (nodeIds.has(node.id)) {
        errors.push({
          code: 'DUPLICATE_NODE_ID',
          message: `节点ID ${node.id} 重复`,
          field: `nodes[${index}].id`,
          severity: 'error'
        })
      } else {
        nodeIds.add(node.id)
      }
    }

    // 检查数据类型
    if (node.dataType && typeof node.dataType !== 'object') {
      warnings.push({
        code: 'INVALID_DATA_TYPE',
        message: `节点 ${node.id || index} 的dataType应该是对象`,
        field: `nodes[${index}].dataType`,
        suggestion: 'dataType应该包含type、length等字段'
      })
    }
  })
}

/**
 * 验证边数据
 */
function validateEdges(
  edges: any[],
  nodes: any[],
  errors: ValidationError[],
  warnings: ValidationWarning[],
  suggestions: string[]
) {
  const edgeIds = new Set<string>()
  const nodeIds = new Set(nodes.map(node => node.id).filter(Boolean))

  edges.forEach((edge, index) => {
    if (!edge || typeof edge !== 'object') {
      errors.push({
        code: 'INVALID_EDGE',
        message: `边 ${index} 数据格式错误`,
        field: `edges[${index}]`,
        severity: 'error'
      })
      return
    }

    // 检查必需字段
    const requiredEdgeFields = ['id', 'source', 'target']
    requiredEdgeFields.forEach(field => {
      if (!edge[field]) {
        errors.push({
          code: 'MISSING_EDGE_FIELD',
          message: `边 ${index} 缺少必需字段: ${field}`,
          field: `edges[${index}].${field}`,
          severity: 'error'
        })
      }
    })

    // 检查ID唯一性
    if (edge.id) {
      if (edgeIds.has(edge.id)) {
        errors.push({
          code: 'DUPLICATE_EDGE_ID',
          message: `边ID ${edge.id} 重复`,
          field: `edges[${index}].id`,
          severity: 'error'
        })
      } else {
        edgeIds.add(edge.id)
      }
    }

    // 检查源和目标节点是否存在
    if (edge.source && !nodeIds.has(edge.source)) {
      warnings.push({
        code: 'MISSING_SOURCE_NODE',
        message: `边 ${edge.id || index} 的源节点 ${edge.source} 不存在`,
        field: `edges[${index}].source`,
        suggestion: '确保源节点在nodes数组中存在'
      })
    }

    if (edge.target && !nodeIds.has(edge.target)) {
      warnings.push({
        code: 'MISSING_TARGET_NODE',
        message: `边 ${edge.id || index} 的目标节点 ${edge.target} 不存在`,
        field: `edges[${index}].target`,
        suggestion: '确保目标节点在nodes数组中存在'
      })
    }

    // 检查置信度
    if (edge.confidence !== undefined) {
      if (typeof edge.confidence !== 'number' || edge.confidence < 0 || edge.confidence > 1) {
        warnings.push({
          code: 'INVALID_CONFIDENCE',
          message: `边 ${edge.id || index} 的置信度应该是0-1之间的数字`,
          field: `edges[${index}].confidence`,
          suggestion: '置信度用于表示血缘关系的可信程度'
        })
      }
    }
  })
}

/**
 * 验证数据一致性
 */
function validateDataConsistency(
  lineageData: any,
  errors: ValidationError[],
  warnings: ValidationWarning[],
  suggestions: string[]
) {
  // 检查表和节点的一致性
  const tableNames = Object.keys(lineageData.tables)
  const nodeTableNames = new Set(lineageData.nodes.map((node: any) => node.tableName).filter(Boolean))

  // 检查是否有节点引用了不存在的表
  nodeTableNames.forEach(tableName => {
    if (!tableNames.includes(String(tableName))) {
      warnings.push({
        code: 'ORPHAN_NODE_TABLE',
        message: `节点引用的表 ${tableName} 在tables中不存在`,
        suggestion: '确保所有节点引用的表都在tables对象中定义'
      })
    }
  })

  // 检查是否有表没有对应的节点
  tableNames.forEach(tableName => {
    if (!nodeTableNames.has(tableName)) {
      warnings.push({
        code: 'UNUSED_TABLE',
        message: `表 ${tableName} 没有对应的字段节点`,
        suggestion: '每个表都应该有对应的字段节点'
      })
    }
  })

  // 检查边的连通性
  if (lineageData.edges.length > 0 && lineageData.nodes.length > 1) {
    const connectedNodes = new Set<string>()
    lineageData.edges.forEach((edge: any) => {
      if (edge.source) connectedNodes.add(edge.source)
      if (edge.target) connectedNodes.add(edge.target)
    })

    const isolatedNodes = lineageData.nodes.filter((node: any) =>
      node.id && !connectedNodes.has(node.id)
    )

    if (isolatedNodes.length > 0) {
      warnings.push({
        code: 'ISOLATED_NODES',
        message: `发现 ${isolatedNodes.length} 个孤立节点`,
        suggestion: '孤立节点不参与血缘关系，考虑添加相关的边或移除这些节点'
      })
    }
  }
}

/**
 * 根据转换类型获取边颜色
 * @param transformType 转换类型
 * @returns 颜色值
 */
export function getEdgeColorByTransformType(transformType?: string): string {
  const colorMap: Record<string, string> = {
    'DIRECT': '#1890ff',      // 直接映射 - 蓝色
    'JOIN': '#52c41a',        // 连接 - 绿色
    'AGGREGATE': '#fa8c16',   // 聚合 - 橙色
    'FILTER': '#722ed1',      // 过滤 - 紫色
    'TRANSFORM': '#eb2f96',   // 转换 - 粉色
    'UNION': '#13c2c2',       // 联合 - 青色
    'WINDOW': '#faad14'       // 窗口函数 - 黄色
  }

  return colorMap[transformType || 'DIRECT'] || '#1890ff'
}

/**
 * 根据置信度获取边宽度
 * @param confidence 置信度 (0-1)
 * @returns 线宽
 */
export function getEdgeWidthByConfidence(confidence?: number): number {
  if (confidence === undefined) return 2

  if (confidence >= 0.9) return 3
  if (confidence >= 0.7) return 2
  if (confidence >= 0.5) return 1.5
  return 1
}

/**
 * 根据转换类型获取边虚线样式
 * @param transformType 转换类型
 * @returns 虚线数组
 */
export function getEdgeDashByTransformType(transformType?: string): number[] | undefined {
  const dashMap: Record<string, number[]> = {
    'DIRECT': [],             // 实线
    'JOIN': [],               // 实线
    'AGGREGATE': [],          // 实线
    'FILTER': [5, 5],         // 虚线
    'TRANSFORM': [3, 3],      // 短虚线
    'UNION': [8, 4],          // 长虚线
    'WINDOW': [2, 2, 8, 2]    // 点划线
  }

  const dash = dashMap[transformType || 'DIRECT']
  return dash && dash.length > 0 ? dash : undefined
}

/**
 * 清理和修复血缘数据
 * @param rawData 原始数据
 * @returns 清理后的数据和修复报告
 */
export function sanitizeLineageData(rawData: any): {
  data: LineageData | null;
  report: {
    fixed: string[];
    removed: string[];
    warnings: string[];
  };
} {
  const report = {
    fixed: [] as string[],
    removed: [] as string[],
    warnings: [] as string[]
  }

  try {
    // 如果数据为空或无效，返回空结果
    if (!rawData || typeof rawData !== 'object') {
      report.warnings.push('输入数据无效，无法修复')
      return { data: null, report }
    }

    // 初始化基本结构
    const sanitizedData: LineageData = {
      tables: {},
      nodes: [],
      edges: [],
      metadata: rawData.metadata || {
        version: '1.0.0',
        parseTime: new Date().toISOString()
      }
    }

    // 清理表数据
    if (rawData.tables && typeof rawData.tables === 'object') {
      Object.entries(rawData.tables).forEach(([key, table]: [string, any]) => {
        if (table && typeof table === 'object') {
          const cleanTable: TableInfo = {
            name: table.name || key,
            type: table.type || 'table',
            fields: [],
            description: table.description,
            rowCount: typeof table.rowCount === 'number' ? table.rowCount : undefined,
            size: table.size
          }

          // 清理字段数据
          if (Array.isArray(table.fields)) {
            table.fields.forEach((field: any, index: number) => {
              if (field && typeof field === 'object' && field.fieldName) {
                const cleanField: LineageNode = {
                  id: field.id || `${cleanTable.name}.${field.fieldName}`,
                  label: field.label || field.fieldName,
                  tableName: cleanTable.name,
                  fieldName: field.fieldName,
                  type: 'field',
                  dataType: field.dataType,
                  description: field.description,
                  isKey: Boolean(field.isKey),
                  isNullable: field.isNullable !== false,
                  defaultValue: field.defaultValue
                }
                cleanTable.fields.push(cleanField)
                sanitizedData.nodes.push(cleanField)
              } else {
                report.removed.push(`表 ${cleanTable.name} 的无效字段 ${index}`)
              }
            })
          }

          sanitizedData.tables[cleanTable.name] = cleanTable
          if (table.name !== key) {
            report.fixed.push(`修正表名不匹配: ${key} -> ${table.name}`)
          }
        } else {
          report.removed.push(`无效的表数据: ${key}`)
        }
      })
    }

    // 清理边数据
    if (Array.isArray(rawData.edges)) {
      const nodeIds = new Set(sanitizedData.nodes.map(node => node.id))

      rawData.edges.forEach((edge: any, index: number) => {
        if (edge && typeof edge === 'object' && edge.source && edge.target) {
          // 检查源和目标节点是否存在
          if (nodeIds.has(edge.source) && nodeIds.has(edge.target)) {
            const cleanEdge: LineageEdge = {
              id: edge.id || generateUniqueId('edge'),
              source: edge.source,
              target: edge.target,
              label: edge.label,
              transformType: edge.transformType || 'DIRECT',
              expression: edge.expression,
              confidence: typeof edge.confidence === 'number' &&
                         edge.confidence >= 0 && edge.confidence <= 1
                         ? edge.confidence : undefined
            }
            sanitizedData.edges.push(cleanEdge)
          } else {
            report.removed.push(`边 ${edge.id || index}: 源或目标节点不存在`)
          }
        } else {
          report.removed.push(`无效的边数据: ${index}`)
        }
      })
    }

    // 添加额外的节点数据（如果原始数据中有独立的nodes数组）
    if (Array.isArray(rawData.nodes)) {
      const existingNodeIds = new Set(sanitizedData.nodes.map(node => node.id))

      rawData.nodes.forEach((node: any, index: number) => {
        if (node && typeof node === 'object' && node.id && !existingNodeIds.has(node.id)) {
          if (node.fieldName && node.tableName) {
            const cleanNode: LineageNode = {
              id: node.id,
              label: node.label || node.fieldName,
              tableName: node.tableName,
              fieldName: node.fieldName,
              type: 'field',
              dataType: node.dataType,
              description: node.description,
              isKey: Boolean(node.isKey),
              isNullable: node.isNullable !== false,
              defaultValue: node.defaultValue
            }
            sanitizedData.nodes.push(cleanNode)
            report.fixed.push(`添加独立节点: ${node.id}`)
          } else {
            report.removed.push(`无效的节点数据: ${index}`)
          }
        }
      })
    }

    // 生成报告
    if (Object.keys(sanitizedData.tables).length === 0) {
      report.warnings.push('没有有效的表数据')
    }
    if (sanitizedData.nodes.length === 0) {
      report.warnings.push('没有有效的字段节点')
    }
    if (sanitizedData.edges.length === 0) {
      report.warnings.push('没有有效的血缘关系')
    }

    return { data: sanitizedData, report }
  } catch (error) {
    report.warnings.push(`数据清理过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
    return { data: null, report }
  }
}

/**
 * 创建空的血缘数据结构
 * @param message 提示信息
 * @returns 空的血缘数据
 */
export function createEmptyLineageData(message?: string): LineageData {
  return {
    tables: {},
    nodes: [],
    edges: [],
    metadata: {
      version: '1.0.0',
      parseTime: new Date().toISOString(),
      message: message || '空数据集'
    }
  }
}

/**
 * 检查数据是否为空
 * @param data 血缘数据
 * @returns 是否为空
 */
export function isEmptyLineageData(data: LineageData | null | undefined): boolean {
  if (!data) return true

  return Object.keys(data.tables).length === 0 &&
         data.nodes.length === 0 &&
         data.edges.length === 0
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  let previous = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func.apply(null, args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func.apply(null, args)
      }, remaining)
    }
  }
}

/**
 * 从原始JSON数据转换为标准血缘数据格式
 * @param rawData 原始JSON数据
 * @returns 标准血缘数据
 */
export function transformRawDataToLineageData(rawData: any): LineageData {
  // 如果已经是标准格式，直接返回
  if (rawData.tables && rawData.nodes && rawData.edges) {
    return rawData as LineageData
  }

  const tables: { [key: string]: TableInfo } = {}
  const nodes: LineageNode[] = []
  const edges: LineageEdge[] = []

  // 处理不同格式的原始数据
  if (Array.isArray(rawData)) {
    // 如果是数组格式，假设是表的数组
    rawData.forEach((tableData: any, index: number) => {
      const tableName = tableData.name || tableData.tableName || `table_${index}`
      const table = processTableData(tableData, tableName)
      tables[tableName] = table
      nodes.push(...table.fields)
    })
  } else if (rawData.tables) {
    // 如果有tables字段
    if (Array.isArray(rawData.tables)) {
      rawData.tables.forEach((tableData: any) => {
        const tableName = tableData.name || tableData.tableName
        if (tableName) {
          const table = processTableData(tableData, tableName)
          tables[tableName] = table
          nodes.push(...table.fields)
        }
      })
    } else {
      // tables是对象格式
      Object.entries(rawData.tables).forEach(([tableName, tableData]: [string, any]) => {
        const table = processTableData(tableData, tableName)
        tables[tableName] = table
        nodes.push(...table.fields)
      })
    }
  }

  // 处理边数据
  if (rawData.edges && Array.isArray(rawData.edges)) {
    rawData.edges.forEach((edgeData: any) => {
      const edge = processEdgeData(edgeData)
      if (edge) {
        edges.push(edge)
      }
    })
  } else if (rawData.relationships && Array.isArray(rawData.relationships)) {
    rawData.relationships.forEach((edgeData: any) => {
      const edge = processEdgeData(edgeData)
      if (edge) {
        edges.push(edge)
      }
    })
  }

  return {
    tables,
    nodes,
    edges,
    metadata: {
      sqlText: rawData.sql || rawData.sqlText || '',
      parseTime: new Date().toISOString(),
      version: '1.0.0'
    }
  }
}

/**
 * 处理表数据
 * @param tableData 原始表数据
 * @param tableName 表名
 * @returns 标准表信息
 */
function processTableData(tableData: any, tableName: string): TableInfo {
  const fields: LineageNode[] = []

  // 处理字段数据
  const fieldsData = tableData.fields || tableData.columns || []
  fieldsData.forEach((fieldData: any, index: number) => {
    const fieldName = fieldData.name || fieldData.fieldName || fieldData.columnName || `field_${index}`
    const field: LineageNode = {
      id: `${tableName}.${fieldName}`,
      label: fieldName,
      tableName,
      fieldName,
      type: 'field',
      dataType: fieldData.dataType || fieldData.type || { type: 'VARCHAR' },
      description: fieldData.description || fieldData.comment,
      isKey: fieldData.isKey || fieldData.isPrimaryKey || false,
      isNullable: fieldData.isNullable !== false,
      defaultValue: fieldData.defaultValue
    }
    fields.push(field)
  })

  return {
    name: tableName,
    type: tableData.type || 'table',
    fields,
    description: tableData.description || tableData.comment,
    position: tableData.position,
    rowCount: tableData.rowCount,
    size: tableData.size
  }
}

/**
 * 处理边数据
 * @param edgeData 原始边数据
 * @returns 标准边信息
 */
function processEdgeData(edgeData: any): LineageEdge | null {
  if (!edgeData.source || !edgeData.target) {
    return null
  }

  return {
    id: edgeData.id || generateUniqueId('edge'),
    source: edgeData.source || edgeData.sourceField,
    target: edgeData.target || edgeData.targetField,
    label: edgeData.label || edgeData.type,
    transformType: edgeData.transformType || edgeData.type || 'DIRECT',
    expression: edgeData.expression || edgeData.sql,
    confidence: edgeData.confidence || 1.0
  }
}

/**
 * 优化图数据布局
 * @param g6Data G6图数据
 * @param options 布局选项
 * @returns 优化后的G6图数据
 */
export function optimizeGraphLayout(
  g6Data: G6GraphData,
  options: {
    nodeSpacing?: number;
    rankSpacing?: number;
    direction?: 'LR' | 'TB' | 'RL' | 'BT';
  } = {}
): G6GraphData {
  const { nodeSpacing = 80, rankSpacing = 150, direction = 'LR' } = options

  // 创建深拷贝避免修改原数据
  const optimizedData = deepClone(g6Data)

  // 如果没有位置信息，使用简单的层次布局
  if (optimizedData.nodes.some(node => !node.x || !node.y)) {
    const layers = calculateNodeLayers(optimizedData)
    applyLayeredLayout(optimizedData, layers, { nodeSpacing, rankSpacing, direction })
  }

  return optimizedData
}

/**
 * 计算节点层次
 * @param g6Data G6图数据
 * @returns 节点层次映射
 */
function calculateNodeLayers(g6Data: G6GraphData): Map<string, number> {
  const layers = new Map<string, number>()
  const visited = new Set<string>()
  const inDegree = new Map<string, number>()

  // 计算入度
  g6Data.nodes.forEach(node => {
    inDegree.set(node.id, 0)
  })

  g6Data.edges.forEach(edge => {
    const targetDegree = inDegree.get(edge.target) || 0
    inDegree.set(edge.target, targetDegree + 1)
  })

  // 拓扑排序分层
  let currentLayer = 0
  let queue = g6Data.nodes.filter(node => (inDegree.get(node.id) || 0) === 0)

  while (queue.length > 0) {
    const nextQueue: any[] = []

    queue.forEach(node => {
      layers.set(node.id, currentLayer)
      visited.add(node.id)

      // 找到所有出边的目标节点
      g6Data.edges.forEach(edge => {
        if (edge.source === node.id) {
          const targetDegree = inDegree.get(edge.target) || 0
          inDegree.set(edge.target, targetDegree - 1)

          if (inDegree.get(edge.target) === 0 && !visited.has(edge.target)) {
            const targetNode = g6Data.nodes.find(n => n.id === edge.target)
            if (targetNode && !nextQueue.includes(targetNode)) {
              nextQueue.push(targetNode)
            }
          }
        }
      })
    })

    queue = nextQueue
    currentLayer++
  }

  return layers
}

/**
 * 应用分层布局
 * @param g6Data G6图数据
 * @param layers 节点层次映射
 * @param options 布局选项
 */
function applyLayeredLayout(
  g6Data: G6GraphData,
  layers: Map<string, number>,
  options: {
    nodeSpacing: number;
    rankSpacing: number;
    direction: 'LR' | 'TB' | 'RL' | 'BT';
  }
): void {
  const { nodeSpacing, rankSpacing, direction } = options
  const layerNodes = new Map<number, any[]>()

  // 按层分组节点
  g6Data.nodes.forEach(node => {
    const layer = layers.get(node.id) || 0
    if (!layerNodes.has(layer)) {
      layerNodes.set(layer, [])
    }
    layerNodes.get(layer)!.push(node)
  })

  // 为每层的节点分配位置
  layerNodes.forEach((nodes, layer) => {
    nodes.forEach((node, index) => {
      const [nodeWidth, nodeHeight] = calculateNodeSize(node.fields || [])

      switch (direction) {
        case 'LR': // 从左到右
          node.x = layer * rankSpacing
          node.y = (index - (nodes.length - 1) / 2) * (nodeHeight + nodeSpacing)
          break
        case 'TB': // 从上到下
          node.x = (index - (nodes.length - 1) / 2) * (nodeWidth + nodeSpacing)
          node.y = layer * rankSpacing
          break
        case 'RL': // 从右到左
          node.x = -layer * rankSpacing
          node.y = (index - (nodes.length - 1) / 2) * (nodeHeight + nodeSpacing)
          break
        case 'BT': // 从下到上
          node.x = (index - (nodes.length - 1) / 2) * (nodeWidth + nodeSpacing)
          node.y = -layer * rankSpacing
          break
      }
    })
  })
}

/**
 * 数据统计信息
 * @param lineageData 血缘数据
 * @returns 统计信息
 */
export function getDataStatistics(lineageData: LineageData): {
  tableCount: number;
  fieldCount: number;
  edgeCount: number;
  maxFieldsPerTable: number;
  avgFieldsPerTable: number;
  transformTypes: Record<string, number>;
  confidenceDistribution: {
    high: number; // >= 0.8
    medium: number; // 0.5 - 0.8
    low: number; // < 0.5
  };
} {
  const tableCount = Object.keys(lineageData.tables).length
  const fieldCount = lineageData.nodes.length
  const edgeCount = lineageData.edges.length

  // 计算每个表的字段数
  const fieldsPerTable = Object.values(lineageData.tables).map(table => table.fields.length)
  const maxFieldsPerTable = Math.max(...fieldsPerTable, 0)
  const avgFieldsPerTable = fieldsPerTable.length > 0
    ? fieldsPerTable.reduce((sum, count) => sum + count, 0) / fieldsPerTable.length
    : 0

  // 统计转换类型
  const transformTypes: Record<string, number> = {}
  lineageData.edges.forEach(edge => {
    const type = edge.transformType || 'DIRECT'
    transformTypes[type] = (transformTypes[type] || 0) + 1
  })

  // 统计置信度分布
  const confidenceDistribution = {
    high: 0,
    medium: 0,
    low: 0
  }

  lineageData.edges.forEach(edge => {
    const confidence = edge.confidence || 1.0
    if (confidence >= 0.8) {
      confidenceDistribution.high++
    } else if (confidence >= 0.5) {
      confidenceDistribution.medium++
    } else {
      confidenceDistribution.low++
    }
  })

  return {
    tableCount,
    fieldCount,
    edgeCount,
    maxFieldsPerTable,
    avgFieldsPerTable: Math.round(avgFieldsPerTable * 100) / 100,
    transformTypes,
    confidenceDistribution
  }
}

// ===== Dagre布局算法集成 =====

/**
 * Dagre布局配置接口
 */
export interface DagreLayoutOptions {
  /** 布局方向：LR(左到右), TB(上到下), RL(右到左), BT(下到上) */
  rankdir?: 'LR' | 'TB' | 'RL' | 'BT';
  /** 节点对齐方式：UL(上左), UR(上右), DL(下左), DR(下右) */
  align?: 'UL' | 'UR' | 'DL' | 'DR';
  /** 同层节点间距 */
  nodesep?: number;
  /** 不同层级间距 */
  ranksep?: number;
  /** 边的分离度 */
  edgesep?: number;
  /** 是否启用性能优化 */
  enableOptimization?: boolean;
  /** 大数据量阈值（超过此数量启用优化） */
  largeDataThreshold?: number;
}

/**
 * 使用Dagre算法进行图布局
 * @param g6Data G6图数据
 * @param options Dagre布局选项
 * @returns 布局后的G6图数据
 */
export function applyDagreLayout(
  g6Data: G6GraphData,
  options: DagreLayoutOptions = {}
): G6GraphData {
  const {
    rankdir = 'LR',
    align = 'UL',
    nodesep = 80,
    ranksep = 150,
    edgesep = 10,
    enableOptimization = true,
    largeDataThreshold = 100
  } = options

  // 创建深拷贝避免修改原数据
  const layoutData = deepClone(g6Data)

  // 检查是否需要性能优化
  const isLargeData = layoutData.nodes.length > largeDataThreshold

  if (isLargeData && enableOptimization) {
    console.log(`检测到大数据量(${layoutData.nodes.length}个节点)，启用性能优化`)
    return applyOptimizedDagreLayout(layoutData, options)
  }

  // 创建Dagre图实例
  const dagreGraph = new dagre.graphlib.Graph()

  // 设置图的默认属性
  dagreGraph.setDefaultEdgeLabel(() => ({}))
  dagreGraph.setGraph({
    rankdir,
    align,
    nodesep,
    ranksep,
    edgesep,
    marginx: 20,
    marginy: 20
  })

  // 添加节点到Dagre图
  layoutData.nodes.forEach(node => {
    const [width, height] = calculateNodeSize(node.fields || [])
    dagreGraph.setNode(node.id, {
      width: width + 20, // 添加一些边距
      height: height + 20,
      id: node.id
    })
  })

  // 添加边到Dagre图
  layoutData.edges.forEach(edge => {
    dagreGraph.setEdge(edge.source, edge.target, {
      id: edge.id
    })
  })

  // 执行布局计算
  try {
    dagre.layout(dagreGraph)
  } catch (error) {
    console.error('Dagre布局计算失败:', error)
    // 降级到简单布局
    return optimizeGraphLayout(layoutData, {
      direction: rankdir,
      nodeSpacing: nodesep,
      rankSpacing: ranksep
    })
  }

  // 将布局结果应用到节点
  layoutData.nodes.forEach(node => {
    const dagreNode = dagreGraph.node(node.id)
    if (dagreNode) {
      node.x = dagreNode.x
      node.y = dagreNode.y
    }
  })

  console.log(`Dagre布局完成: ${layoutData.nodes.length}个节点, ${layoutData.edges.length}条边`)
  return layoutData
}

/**
 * 异步使用Dagre算法进行图布局
 * @param g6Data G6图数据
 * @param options Dagre布局选项
 * @returns Promise<布局后的G6图数据>
 */
export async function applyDagreLayoutAsync(
  g6Data: G6GraphData,
  options: DagreLayoutOptions = {}
): Promise<G6GraphData> {
  return new Promise((resolve) => {
    // 使用 setTimeout 将布局计算移到下一个事件循环
    setTimeout(() => {
      try {
        const result = applyDagreLayout(g6Data, options)
        resolve(result)
      } catch (error) {
        console.error('异步Dagre布局失败:', error)
        // 返回原始数据而不是抛出错误
        resolve(g6Data)
      }
    }, 0)
  })
}

/**
 * 大数据量优化的Dagre布局
 * @param g6Data G6图数据
 * @param options 布局选项
 * @returns 布局后的G6图数据
 */
function applyOptimizedDagreLayout(
  g6Data: G6GraphData,
  options: DagreLayoutOptions
): G6GraphData {
  const {
    rankdir = 'LR',
    align = 'UL',
    nodesep = 80,
    ranksep = 150,
    edgesep = 10
  } = options

  console.log('开始优化布局计算...')
  const startTime = performance.now()

  // 1. 分析图结构，识别关键路径
  const criticalPaths = findCriticalPaths(g6Data)

  // 2. 对关键路径使用精确布局
  const criticalNodes = new Set<string>()
  criticalPaths.forEach(path => {
    path.forEach(nodeId => criticalNodes.add(nodeId))
  })

  // 3. 创建简化的Dagre图（只包含关键节点）
  const simplifiedGraph = new dagre.graphlib.Graph()
  simplifiedGraph.setDefaultEdgeLabel(() => ({}))
  simplifiedGraph.setGraph({
    rankdir,
    align,
    nodesep: nodesep * 1.5, // 增大间距以容纳非关键节点
    ranksep: ranksep * 1.2,
    edgesep,
    marginx: 20,
    marginy: 20
  })

  // 添加关键节点
  g6Data.nodes.forEach(node => {
    if (criticalNodes.has(node.id)) {
      const [width, height] = calculateNodeSize(node.fields || [])
      simplifiedGraph.setNode(node.id, {
        width: width + 20,
        height: height + 20,
        id: node.id
      })
    }
  })

  // 添加关键边
  g6Data.edges.forEach(edge => {
    if (criticalNodes.has(edge.source) && criticalNodes.has(edge.target)) {
      simplifiedGraph.setEdge(edge.source, edge.target, {
        id: edge.id
      })
    }
  })

  // 4. 执行简化布局
  try {
    dagre.layout(simplifiedGraph)
  } catch (error) {
    console.error('优化布局计算失败:', error)
    return optimizeGraphLayout(g6Data, {
      direction: rankdir,
      nodeSpacing: nodesep,
      rankSpacing: ranksep
    })
  }

  // 5. 应用关键节点位置
  g6Data.nodes.forEach(node => {
    if (criticalNodes.has(node.id)) {
      const dagreNode = simplifiedGraph.node(node.id)
      if (dagreNode) {
        node.x = dagreNode.x
        node.y = dagreNode.y
      }
    }
  })

  // 6. 为非关键节点分配位置（基于关键节点的相对位置）
  assignNonCriticalNodePositions(g6Data, criticalNodes, { nodesep, ranksep, rankdir })

  const endTime = performance.now()
  console.log(`优化布局完成，耗时: ${Math.round(endTime - startTime)}ms`)

  return g6Data
}

/**
 * 查找图中的关键路径
 * @param g6Data G6图数据
 * @returns 关键路径数组
 */
function findCriticalPaths(g6Data: G6GraphData): string[][] {
  const paths: string[][] = []
  const visited = new Set<string>()

  // 找到所有源节点（入度为0的节点）
  const inDegree = new Map<string, number>()
  g6Data.nodes.forEach(node => {
    inDegree.set(node.id, 0)
  })

  g6Data.edges.forEach(edge => {
    const targetDegree = inDegree.get(edge.target) || 0
    inDegree.set(edge.target, targetDegree + 1)
  })

  const sourceNodes = g6Data.nodes.filter(node => (inDegree.get(node.id) || 0) === 0)

  // 从每个源节点开始DFS查找最长路径
  sourceNodes.forEach(sourceNode => {
    if (!visited.has(sourceNode.id)) {
      const path = findLongestPath(g6Data, sourceNode.id, visited)
      if (path.length > 1) {
        paths.push(path)
      }
    }
  })

  return paths
}

/**
 * 查找从指定节点开始的最长路径
 * @param g6Data G6图数据
 * @param startNodeId 起始节点ID
 * @param globalVisited 全局访问记录
 * @returns 最长路径
 */
function findLongestPath(
  g6Data: G6GraphData,
  startNodeId: string,
  globalVisited: Set<string>
): string[] {
  const path: string[] = [startNodeId]
  const localVisited = new Set<string>([startNodeId])

  let currentNodeId = startNodeId

  while (true) {
    // 找到当前节点的所有出边
    const outEdges = g6Data.edges.filter(edge =>
      edge.source === currentNodeId && !localVisited.has(edge.target)
    )

    if (outEdges.length === 0) {
      break
    }

    // 选择度数最高的目标节点（更可能是关键节点）
    let nextNodeId = outEdges[0].target
    let maxDegree = getNodeDegree(g6Data, nextNodeId)

    outEdges.forEach(edge => {
      const degree = getNodeDegree(g6Data, edge.target)
      if (degree > maxDegree) {
        maxDegree = degree
        nextNodeId = edge.target
      }
    })

    path.push(nextNodeId)
    localVisited.add(nextNodeId)
    globalVisited.add(nextNodeId)
    currentNodeId = nextNodeId
  }

  return path
}

/**
 * 获取节点的度数（入度+出度）
 * @param g6Data G6图数据
 * @param nodeId 节点ID
 * @returns 节点度数
 */
function getNodeDegree(g6Data: G6GraphData, nodeId: string): number {
  let degree = 0
  g6Data.edges.forEach(edge => {
    if (edge.source === nodeId || edge.target === nodeId) {
      degree++
    }
  })
  return degree
}

/**
 * 为非关键节点分配位置
 * @param g6Data G6图数据
 * @param criticalNodes 关键节点集合
 * @param options 布局选项
 */
function assignNonCriticalNodePositions(
  g6Data: G6GraphData,
  criticalNodes: Set<string>,
  options: {
    nodesep: number;
    ranksep: number;
    rankdir: 'LR' | 'TB' | 'RL' | 'BT';
  }
): void {
  const { nodesep, ranksep, rankdir } = options

  // 为每个非关键节点找到最近的关键节点
  g6Data.nodes.forEach(node => {
    if (!criticalNodes.has(node.id)) {
      // 找到与该节点相连的关键节点
      const connectedCriticalNodes = findConnectedCriticalNodes(g6Data, node.id, criticalNodes)

      if (connectedCriticalNodes.length > 0) {
        // 基于连接的关键节点计算位置
        const avgPosition = calculateAveragePosition(g6Data, connectedCriticalNodes)

        // 添加一些偏移避免重叠
        const offset = Math.random() * nodesep - nodesep / 2

        switch (rankdir) {
          case 'LR':
            node.x = avgPosition.x + offset
            node.y = avgPosition.y + offset
            break
          case 'TB':
            node.x = avgPosition.x + offset
            node.y = avgPosition.y + offset
            break
          case 'RL':
            node.x = avgPosition.x - offset
            node.y = avgPosition.y + offset
            break
          case 'BT':
            node.x = avgPosition.x + offset
            node.y = avgPosition.y - offset
            break
        }
      } else {
        // 如果没有连接的关键节点，使用简单的网格布局
        const index = g6Data.nodes.findIndex(n => n.id === node.id)
        const gridSize = Math.ceil(Math.sqrt(g6Data.nodes.length))
        const row = Math.floor(index / gridSize)
        const col = index % gridSize

        node.x = col * nodesep
        node.y = row * nodesep
      }
    }
  })
}

/**
 * 查找与指定节点相连的关键节点
 * @param g6Data G6图数据
 * @param nodeId 节点ID
 * @param criticalNodes 关键节点集合
 * @returns 相连的关键节点ID数组
 */
function findConnectedCriticalNodes(
  g6Data: G6GraphData,
  nodeId: string,
  criticalNodes: Set<string>
): string[] {
  const connected: string[] = []

  g6Data.edges.forEach(edge => {
    if (edge.source === nodeId && criticalNodes.has(edge.target)) {
      connected.push(edge.target)
    } else if (edge.target === nodeId && criticalNodes.has(edge.source)) {
      connected.push(edge.source)
    }
  })

  return connected
}

/**
 * 计算节点的平均位置
 * @param g6Data G6图数据
 * @param nodeIds 节点ID数组
 * @returns 平均位置
 */
function calculateAveragePosition(
  g6Data: G6GraphData,
  nodeIds: string[]
): { x: number; y: number } {
  let totalX = 0
  let totalY = 0
  let count = 0

  nodeIds.forEach(nodeId => {
    const node = g6Data.nodes.find(n => n.id === nodeId)
    if (node && typeof node.x === 'number' && typeof node.y === 'number') {
      totalX += node.x
      totalY += node.y
      count++
    }
  })

  return count > 0
    ? { x: totalX / count, y: totalY / count }
    : { x: 0, y: 0 }
}

/**
 * 获取combo标签
 * @param comboId combo ID
 * @returns combo标签
 */
export function getComboLabel(comboId: string): string {
  const labelMap: Record<string, string> = {
    'source_layer': '源数据层',
    'staging_layer': '暂存层',
    'mart_layer': '数据集市层',
    'ods_layer': 'ODS层',
    'dwd_layer': 'DWD层',
    'dws_layer': 'DWS层',
    'ads_layer': 'ADS层'
  }
  return labelMap[comboId] || comboId
}

/**
 * 获取combo颜色
 * @param comboId combo ID
 * @returns combo颜色
 */
export function getComboColor(comboId: string): string {
  const colorMap: Record<string, string> = {
    'source_layer': '#e6f7ff',
    'staging_layer': '#f6ffed',
    'mart_layer': '#fff2e8',
    'ods_layer': '#f9f0ff',
    'dwd_layer': '#e6fffb',
    'dws_layer': '#fff1f0',
    'ads_layer': '#feffe6'
  }
  return colorMap[comboId] || '#f5f5f5'
}

/**
 * 获取combo边框颜色
 * @param comboId combo ID
 * @returns combo边框颜色
 */
export function getComboStrokeColor(comboId: string): string {
  const strokeColorMap: Record<string, string> = {
    'source_layer': '#1890ff',
    'staging_layer': '#52c41a',
    'mart_layer': '#fa8c16',
    'ods_layer': '#722ed1',
    'dwd_layer': '#13c2c2',
    'dws_layer': '#f5222d',
    'ads_layer': '#fadb14'
  }
  return strokeColorMap[comboId] || '#d9d9d9'
}
