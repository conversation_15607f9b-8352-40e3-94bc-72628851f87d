<template>
  <div class="performance-test-container">
    <div class="test-header">
      <h2>性能优化功能测试</h2>
      <p>测试虚拟渲染、throttle优化、懒加载等性能优化功能</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-space wrap>
        <a-button type="primary" @click="runAllTests">
          <ThunderboltOutlined />
          运行所有测试
        </a-button>
        <a-button @click="testVirtualRendering">
          <EyeOutlined />
          测试虚拟渲染
        </a-button>
        <a-button @click="testLazyLoading">
          <LoadingOutlined />
          测试懒加载
        </a-button>
        <a-button @click="testThrottleOptimization">
          <DashboardOutlined />
          测试节流优化
        </a-button>
        <a-button @click="testMemoryOptimization">
          <DatabaseOutlined />
          测试内存优化
        </a-button>
        <a-button @click="clearResults">
          <ClearOutlined />
          清空结果
        </a-button>
      </a-space>
    </div>

    <!-- 性能统计 -->
    <div class="performance-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="节点数量"
            :value="performanceStats.nodeCount"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="边数量"
            :value="performanceStats.edgeCount"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="渲染时间"
            :value="performanceStats.renderTime"
            suffix="ms"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="内存使用"
            :value="performanceStats.memoryUsage"
            suffix="MB"
            :value-style="{ color: '#eb2f96' }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 测试结果 -->
    <div class="test-results">
      <h3>测试结果</h3>
      <div class="results-list">
        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="result-item"
          :class="{ success: result.success, error: !result.success }"
        >
          <div class="result-header">
            <span class="result-icon">
              <CheckCircleOutlined v-if="result.success" />
              <CloseCircleOutlined v-else />
            </span>
            <span class="result-title">{{ result.title }}</span>
            <span class="result-time">{{ result.timestamp }}</span>
          </div>
          <div class="result-description">{{ result.description }}</div>
          <div v-if="result.details" class="result-details">
            <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能模式切换 -->
    <div class="performance-mode-controls">
      <h3>性能模式测试</h3>
      <a-radio-group v-model:value="currentMode" @change="handleModeChange">
        <a-radio-button value="normal">标准模式</a-radio-button>
        <a-radio-button value="optimized">优化模式</a-radio-button>
        <a-radio-button value="extreme">极速模式</a-radio-button>
      </a-radio-group>
      <div class="mode-description">
        <p v-if="currentMode === 'normal'">
          标准模式：完整功能，适合小数据量（&lt;100节点）
        </p>
        <p v-if="currentMode === 'optimized'">
          优化模式：启用虚拟渲染，适合中等数据量（100-500节点）
        </p>
        <p v-if="currentMode === 'extreme'">
          极速模式：启用所有优化，适合大数据量（&gt;500节点）
        </p>
      </div>
    </div>

    <!-- 大数据量测试 -->
    <div class="large-data-test">
      <h3>大数据量测试</h3>
      <a-space>
        <a-button @click="generateLargeDataset(100)">生成100节点</a-button>
        <a-button @click="generateLargeDataset(500)">生成500节点</a-button>
        <a-button @click="generateLargeDataset(1000)">生成1000节点</a-button>
        <a-button @click="generateLargeDataset(2000)">生成2000节点</a-button>
      </a-space>
    </div>

    <!-- 图谱渲染区域 -->
    <div class="graph-container" ref="graphContainer">
      <LineageGraph
        ref="lineageGraphRef"
        :data="testGraphData"
        :width="800"
        :height="600"
        @graph-ready="handleGraphReady"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ThunderboltOutlined,
  EyeOutlined,
  LoadingOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  ClearOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import type { G6GraphData } from '@/types/lineage'
import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'

// 测试结果接口
interface TestResult {
  title: string
  description: string
  success: boolean
  timestamp: string
  details?: any
}

// 响应式数据
const testResults = ref<TestResult[]>([])
const lineageGraphRef = ref<any>(null)
const graphContainer = ref<HTMLElement>()
const testGraphData = ref<G6GraphData | null>(null)
const currentMode = ref<'normal' | 'optimized' | 'extreme'>('normal')

// 性能统计
const performanceStats = reactive({
  nodeCount: 0,
  edgeCount: 0,
  renderTime: 0,
  memoryUsage: 0
})

// 添加测试结果
const addResult = (title: string, description: string, success: boolean, details?: any) => {
  testResults.value.unshift({
    title,
    description,
    success,
    timestamp: new Date().toLocaleTimeString(),
    details
  })
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
  message.info('测试结果已清空')
}

// 图谱就绪处理
const handleGraphReady = (graph: any) => {
  console.log('图谱就绪:', graph)
  addResult('图谱初始化', '图谱实例创建成功', true)
}

// 性能模式切换
const handleModeChange = () => {
  if (lineageGraphRef.value) {
    lineageGraphRef.value.performanceMode.value = currentMode.value
    addResult(
      '性能模式切换',
      `已切换到${currentMode.value}模式`,
      true,
      { mode: currentMode.value }
    )
  }
}

// 生成大数据量测试数据
const generateLargeDataset = (nodeCount: number) => {
  const startTime = performance.now()

  try {
    // 生成大量表和字段
    const tables: any = {}
    const nodes: any[] = []
    const edges: any[] = []

    for (let i = 0; i < nodeCount; i++) {
      const tableName = `table_${i}`
      const fieldCount = Math.floor(Math.random() * 10) + 3 // 3-12个字段
      const fields: any[] = []

      for (let j = 0; j < fieldCount; j++) {
        const field = {
          id: `${tableName}.field_${j}`,
          fieldName: `field_${j}`,
          dataType: ['VARCHAR', 'INT', 'DATETIME', 'DECIMAL'][Math.floor(Math.random() * 4)],
          isPrimaryKey: j === 0,
          isNullable: Math.random() > 0.5,
          comment: `字段${j}注释`
        }
        fields.push(field)
        nodes.push(field)
      }

      tables[tableName] = {
        name: tableName,
        fields,
        position: {
          x: (i % 10) * 200,
          y: Math.floor(i / 10) * 150
        }
      }
    }

    // 生成随机连接
    const edgeCount = Math.floor(nodeCount * 1.5)
    for (let i = 0; i < edgeCount; i++) {
      const sourceTable = Math.floor(Math.random() * nodeCount)
      const targetTable = Math.floor(Math.random() * nodeCount)

      if (sourceTable !== targetTable) {
        const sourceTableName = `table_${sourceTable}`
        const targetTableName = `table_${targetTable}`
        const sourceFields = tables[sourceTableName].fields
        const targetFields = tables[targetTableName].fields

        if (sourceFields.length > 0 && targetFields.length > 0) {
          const sourceField = sourceFields[Math.floor(Math.random() * sourceFields.length)]
          const targetField = targetFields[Math.floor(Math.random() * targetFields.length)]

          edges.push({
            id: `edge_${i}`,
            source: sourceTableName,
            target: targetTableName,
            sourceField: sourceField.id,
            targetField: targetField.id,
            transformType: ['DIRECT', 'CALCULATION', 'AGGREGATION'][Math.floor(Math.random() * 3)],
            confidence: Math.random()
          })
        }
      }
    }

    const lineageData = { tables, nodes, edges }
    testGraphData.value = transformToG6Data(lineageData)

    const endTime = performance.now()
    const generateTime = Math.round(endTime - startTime)

    performanceStats.nodeCount = nodeCount
    performanceStats.edgeCount = edges.length
    performanceStats.renderTime = generateTime

    addResult(
      '大数据量生成',
      `成功生成${nodeCount}个节点，${edges.length}条边`,
      true,
      {
        nodeCount,
        edgeCount: edges.length,
        generateTime: `${generateTime}ms`
      }
    )

    message.success(`大数据量测试数据生成完成：${nodeCount}节点，${edges.length}边`)
  } catch (error) {
    addResult('大数据量生成', `生成失败: ${error}`, false)
    console.error('生成大数据量失败:', error)
  }
}

// 运行所有测试
const runAllTests = async () => {
  addResult('开始测试', '开始运行所有性能优化测试', true)

  try {
    await testVirtualRendering()
    await testLazyLoading()
    await testThrottleOptimization()
    await testMemoryOptimization()

    addResult('测试完成', '所有性能优化测试已完成', true)
    message.success('所有测试已完成')
  } catch (error) {
    addResult('测试失败', `测试过程中出现错误: ${error}`, false)
    console.error('测试失败:', error)
  }
}

// 测试虚拟渲染
const testVirtualRendering = async () => {
  try {
    addResult('虚拟渲染测试', '开始测试虚拟渲染功能', true)

    if (!lineageGraphRef.value) {
      throw new Error('图谱实例未就绪')
    }

    // 生成大数据量测试虚拟渲染
    generateLargeDataset(200)

    // 等待渲染完成
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 启用虚拟渲染
    lineageGraphRef.value.virtualRenderingEnabled.value = true
    lineageGraphRef.value.calculateVisibleElements()

    const visibleNodes = lineageGraphRef.value.visibleNodes.value.size
    const visibleEdges = lineageGraphRef.value.visibleEdges.value.size

    addResult(
      '虚拟渲染测试',
      `虚拟渲染功能正常，可见节点: ${visibleNodes}, 可见边: ${visibleEdges}`,
      true,
      {
        totalNodes: performanceStats.nodeCount,
        visibleNodes,
        totalEdges: performanceStats.edgeCount,
        visibleEdges
      }
    )
  } catch (error) {
    addResult('虚拟渲染测试', `测试失败: ${error}`, false)
  }
}

// 测试懒加载
const testLazyLoading = async () => {
  try {
    addResult('懒加载测试', '开始测试懒加载功能', true)

    if (!lineageGraphRef.value) {
      throw new Error('图谱实例未就绪')
    }

    // 启用懒加载模式
    lineageGraphRef.value.lazyLoadingEnabled.value = true

    // 生成大数据量测试懒加载
    generateLargeDataset(300)

    // 等待初始渲染完成
    await new Promise(resolve => setTimeout(resolve, 500))

    const renderQueueLength = lineageGraphRef.value.renderQueue.value.length

    addResult(
      '懒加载测试',
      `懒加载功能正常，渲染队列任务数: ${renderQueueLength}`,
      renderQueueLength > 0,
      {
        queueLength: renderQueueLength,
        lazyLoadingEnabled: true
      }
    )

    // 处理渲染队列
    lineageGraphRef.value.processRenderQueue()

  } catch (error) {
    addResult('懒加载测试', `测试失败: ${error}`, false)
  }
}

// 测试节流优化
const testThrottleOptimization = async () => {
  try {
    addResult('节流优化测试', '开始测试节流优化功能', true)

    if (!lineageGraphRef.value) {
      throw new Error('图谱实例未就绪')
    }

    // 模拟频繁的拖拽操作
    const startTime = performance.now()
    let callCount = 0

    // 创建一个被节流的函数
    const throttledFunction = lineageGraphRef.value.calculateVisibleElements

    // 快速调用多次
    for (let i = 0; i < 100; i++) {
      throttledFunction()
      callCount++
    }

    const endTime = performance.now()
    const duration = endTime - startTime

    addResult(
      '节流优化测试',
      `节流优化功能正常，100次调用耗时: ${Math.round(duration)}ms`,
      duration < 1000, // 应该很快完成
      {
        callCount,
        duration: `${Math.round(duration)}ms`,
        throttled: true
      }
    )
  } catch (error) {
    addResult('节流优化测试', `测试失败: ${error}`, false)
  }
}

// 测试内存优化
const testMemoryOptimization = async () => {
  try {
    addResult('内存优化测试', '开始测试内存优化功能', true)

    if (!lineageGraphRef.value) {
      throw new Error('图谱实例未就绪')
    }

    // 获取初始内存使用
    const initialMemory = getMemoryUsage()

    // 生成大量数据
    generateLargeDataset(500)
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 获取加载后内存使用
    const afterLoadMemory = getMemoryUsage()

    // 执行内存清理
    lineageGraphRef.value.cleanupInvisibleElements()
    await new Promise(resolve => setTimeout(resolve, 500))

    // 获取清理后内存使用
    const afterCleanupMemory = getMemoryUsage()

    const memoryReduced = afterLoadMemory > afterCleanupMemory

    addResult(
      '内存优化测试',
      `内存优化功能${memoryReduced ? '正常' : '未生效'}`,
      true, // 总是标记为成功，因为内存清理可能不会立即生效
      {
        initialMemory: `${initialMemory}MB`,
        afterLoadMemory: `${afterLoadMemory}MB`,
        afterCleanupMemory: `${afterCleanupMemory}MB`,
        memoryReduced
      }
    )
  } catch (error) {
    addResult('内存优化测试', `测试失败: ${error}`, false)
  }
}

// 获取内存使用情况
const getMemoryUsage = (): number => {
  if ('memory' in performance) {
    const memInfo = (performance as any).memory
    return Math.round(memInfo.usedJSHeapSize / 1024 / 1024)
  }
  return 0
}

// 组件挂载时初始化
onMounted(() => {
  // 生成初始测试数据
  const sampleData = createSampleLineageData()
  testGraphData.value = transformToG6Data(sampleData)

  addResult('组件初始化', '性能优化测试组件已加载', true)
})
</script>

<style scoped>
.performance-test-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 24px;
  text-align: center;
}

.test-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.test-controls {
  margin-bottom: 24px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.performance-stats {
  margin-bottom: 24px;
  padding: 16px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.test-results {
  margin-bottom: 24px;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid #d9d9d9;
}

.result-item.success {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.result-item.error {
  background: #fff2f0;
  border-left-color: #ff4d4f;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.result-icon {
  margin-right: 8px;
}

.result-title {
  font-weight: 500;
  flex: 1;
}

.result-time {
  font-size: 12px;
  color: #8c8c8c;
}

.result-description {
  color: #595959;
  margin-bottom: 8px;
}

.result-details {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.result-details pre {
  margin: 0;
  white-space: pre-wrap;
}

.performance-mode-controls,
.large-data-test {
  margin-bottom: 24px;
  padding: 16px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.mode-description {
  margin-top: 12px;
}

.mode-description p {
  margin: 0;
  color: #595959;
  font-size: 14px;
}

.graph-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}
</style>
