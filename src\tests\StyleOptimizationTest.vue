<template>
  <div class="style-test-container">
    <div class="test-header">
      <h1>样式优化测试页面</h1>
      <p>测试现代扁平化设计风格、表节点卡片样式、字段高亮和连线动画效果</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-button type="primary" @click="loadTestData">加载测试数据</a-button>
        <a-button @click="testFieldHighlight">测试字段高亮</a-button>
        <a-button @click="testPathTracing">测试路径追踪</a-button>
        <a-button @click="toggleTheme">切换主题</a-button>
        <a-button @click="resetStyles">重置样式</a-button>
      </a-space>
    </div>

    <div class="test-content">
      <div class="graph-container">
        <LineageGraph
          ref="graphRef"
          :width="800"
          :height="600"
          :data="testGraphData"
          @fieldHover="handleFieldHover"
          @fieldClick="handleFieldClick"
          @fieldLeave="handleFieldLeave"
        />
      </div>

      <div class="test-info">
        <a-card title="测试信息" size="small">
          <div class="info-item">
            <strong>当前主题:</strong> {{ currentTheme }}
          </div>
          <div class="info-item">
            <strong>悬浮字段:</strong> {{ hoveredField || '无' }}
          </div>
          <div class="info-item">
            <strong>选中字段:</strong> {{ selectedField || '无' }}
          </div>
          <div class="info-item">
            <strong>测试状态:</strong> {{ testStatus }}
          </div>
        </a-card>

        <a-card title="样式特性" size="small" style="margin-top: 16px;">
          <ul class="feature-list">
            <li>✅ 现代扁平化设计风格</li>
            <li>✅ 表节点圆角和阴影效果</li>
            <li>✅ 字段悬浮高亮状态</li>
            <li>✅ 连线动画和渐变效果</li>
            <li>✅ 路径追踪高亮功能</li>
            <li>✅ 深色主题支持</li>
            <li>✅ 响应式交互反馈</li>
          </ul>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import { useLineageStore } from '@/stores/lineageStore'
import type { G6GraphData, LineageNode } from '@/types/lineage'

// Store
const lineageStore = useLineageStore()

// 响应式数据
const graphRef = ref()
const testGraphData = ref<G6GraphData | null>(null)
const hoveredField = ref<string | null>(null)
const selectedField = ref<string | null>(null)
const testStatus = ref('准备就绪')
const currentTheme = ref('light')

// 创建测试数据
const createTestData = (): G6GraphData => {
  return {
    nodes: [
      {
        id: 'users',
        type: 'table-node',
        x: 100,
        y: 200,
        tableName: 'users',
        fields: [
          { id: 'users.id', fieldName: 'id', dataType: { type: 'INT', length: 11 }, isKey: true, label: 'id', tableName: 'users', type: 'field' as const },
          { id: 'users.name', fieldName: 'name', dataType: { type: 'VARCHAR', length: 100 }, label: 'name', tableName: 'users', type: 'field' as const },
          { id: 'users.email', fieldName: 'email', dataType: { type: 'VARCHAR', length: 255 }, label: 'email', tableName: 'users', type: 'field' as const },
          { id: 'users.created_at', fieldName: 'created_at', dataType: { type: 'TIMESTAMP' }, label: 'created_at', tableName: 'users', type: 'field' as const }
        ],
        tableInfo: { name: 'users', type: 'table' as const, fields: [] }
      },
      {
        id: 'orders',
        type: 'table-node',
        x: 400,
        y: 200,
        tableName: 'orders',
        fields: [
          { id: 'orders.id', fieldName: 'id', dataType: { type: 'INT', length: 11 }, isKey: true, label: 'id', tableName: 'orders', type: 'field' as const },
          { id: 'orders.user_id', fieldName: 'user_id', dataType: { type: 'INT', length: 11 }, label: 'user_id', tableName: 'orders', type: 'field' as const },
          { id: 'orders.amount', fieldName: 'amount', dataType: { type: 'DECIMAL', precision: 10, scale: 2 }, label: 'amount', tableName: 'orders', type: 'field' as const },
          { id: 'orders.status', fieldName: 'status', dataType: { type: 'VARCHAR', length: 50 }, label: 'status', tableName: 'orders', type: 'field' as const }
        ],
        tableInfo: { name: 'orders', type: 'table' as const, fields: [] }
      },
      {
        id: 'order_summary',
        type: 'table-node',
        x: 700,
        y: 200,
        tableName: 'order_summary',
        fields: [
          { id: 'order_summary.user_id', fieldName: 'user_id', dataType: { type: 'INT', length: 11 }, label: 'user_id', tableName: 'order_summary', type: 'field' as const },
          { id: 'order_summary.user_name', fieldName: 'user_name', dataType: { type: 'VARCHAR', length: 100 }, label: 'user_name', tableName: 'order_summary', type: 'field' as const },
          { id: 'order_summary.total_amount', fieldName: 'total_amount', dataType: { type: 'DECIMAL', precision: 12, scale: 2 }, label: 'total_amount', tableName: 'order_summary', type: 'field' as const },
          { id: 'order_summary.order_count', fieldName: 'order_count', dataType: { type: 'INT', length: 11 }, label: 'order_count', tableName: 'order_summary', type: 'field' as const }
        ],
        tableInfo: { name: 'order_summary', type: 'table' as const, fields: [] }
      }
    ],
    edges: [
      {
        id: 'edge1',
        source: 'users',
        target: 'orders',
        type: 'field-edge',
        sourceField: 'id',
        targetField: 'user_id',
        sourceFieldId: 'users.id',
        targetFieldId: 'orders.user_id',
        transformType: 'DIRECT',
        confidence: 0.95,
        lineageEdge: {
          id: 'edge1',
          source: 'users.id',
          target: 'orders.user_id',
          transformType: 'DIRECT',
          confidence: 0.95,
          label: '用户关联'
        }
      },
      {
        id: 'edge2',
        source: 'orders',
        target: 'order_summary',
        type: 'field-edge',
        sourceField: 'user_id',
        targetField: 'user_id',
        sourceFieldId: 'orders.user_id',
        targetFieldId: 'order_summary.user_id',
        transformType: 'AGGREGATE',
        confidence: 0.90,
        lineageEdge: {
          id: 'edge2',
          source: 'orders.user_id',
          target: 'order_summary.user_id',
          transformType: 'AGGREGATE',
          confidence: 0.90,
          label: '聚合分组'
        }
      },
      {
        id: 'edge3',
        source: 'users',
        target: 'order_summary',
        type: 'field-edge',
        sourceField: 'name',
        targetField: 'user_name',
        sourceFieldId: 'users.name',
        targetFieldId: 'order_summary.user_name',
        transformType: 'DIRECT',
        confidence: 0.98,
        lineageEdge: {
          id: 'edge3',
          source: 'users.name',
          target: 'order_summary.user_name',
          transformType: 'DIRECT',
          confidence: 0.98,
          label: '用户名映射'
        }
      },
      {
        id: 'edge4',
        source: 'orders',
        target: 'order_summary',
        type: 'field-edge',
        sourceField: 'amount',
        targetField: 'total_amount',
        sourceFieldId: 'orders.amount',
        targetFieldId: 'order_summary.total_amount',
        transformType: 'AGGREGATE',
        confidence: 0.92,
        lineageEdge: {
          id: 'edge4',
          source: 'orders.amount',
          target: 'order_summary.total_amount',
          transformType: 'AGGREGATE',
          confidence: 0.92,
          label: '金额汇总'
        }
      }
    ]
  }
}

// 加载测试数据
const loadTestData = () => {
  testGraphData.value = createTestData()
  testStatus.value = '测试数据已加载'
  message.success('测试数据加载成功')
}

// 测试字段高亮
const testFieldHighlight = () => {
  if (!graphRef.value) {
    message.warning('请先加载测试数据')
    return
  }

  const testFields = ['users.id', 'orders.user_id', 'order_summary.user_id']
  let currentIndex = 0

  const highlightNext = () => {
    if (currentIndex < testFields.length) {
      const fieldId = testFields[currentIndex]
      graphRef.value.setFieldHighlight(fieldId, true)
      hoveredField.value = fieldId
      testStatus.value = `正在高亮字段: ${fieldId}`

      setTimeout(() => {
        graphRef.value.setFieldHighlight(fieldId, false)
        currentIndex++
        if (currentIndex < testFields.length) {
          setTimeout(highlightNext, 500)
        } else {
          testStatus.value = '字段高亮测试完成'
          hoveredField.value = null
        }
      }, 2000)
    }
  }

  highlightNext()
}

// 测试路径追踪
const testPathTracing = () => {
  if (!graphRef.value) {
    message.warning('请先加载测试数据')
    return
  }

  const testField = 'users.id'
  const paths = graphRef.value.findFieldLineagePaths(testField)
  graphRef.value.highlightLineagePaths(paths, true)

  testStatus.value = `正在追踪字段路径: ${testField}`

  setTimeout(() => {
    graphRef.value.highlightLineagePaths(paths, false)
    testStatus.value = '路径追踪测试完成'
  }, 3000)
}

// 切换主题
const toggleTheme = () => {
  const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
  lineageStore.setTheme(newTheme)
  currentTheme.value = newTheme
  testStatus.value = `已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`
  message.info(`已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`)
}

// 重置样式
const resetStyles = () => {
  if (graphRef.value) {
    graphRef.value.setFieldHighlight('', false)
    hoveredField.value = null
    selectedField.value = null
    testStatus.value = '样式已重置'
    message.info('样式已重置')
  }
}

// 事件处理
const handleFieldHover = (fieldId: string, fieldData: LineageNode) => {
  hoveredField.value = fieldId
}

const handleFieldClick = (fieldId: string, fieldData: LineageNode) => {
  selectedField.value = fieldId
  message.info(`点击字段: ${fieldId}`)
}

const handleFieldLeave = (fieldId: string) => {
  hoveredField.value = null
}

// 生命周期
onMounted(() => {
  loadTestData()
  currentTheme.value = lineageStore.theme.mode
})
</script>

<style scoped>
.style-test-container {
  padding: 24px;
  min-height: 100vh;
  background: var(--color-background);
}

.test-header {
  text-align: center;
  margin-bottom: 24px;
}

.test-header h1 {
  color: var(--color-heading);
  margin-bottom: 8px;
}

.test-header p {
  color: var(--color-text-secondary);
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.test-content {
  display: flex;
  gap: 24px;
}

.graph-container {
  flex: 1;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
}

.test-info {
  width: 300px;
}

.info-item {
  margin-bottom: 8px;
  color: var(--color-text);
}

.feature-list {
  margin: 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 4px;
  color: var(--color-text);
}
</style>
