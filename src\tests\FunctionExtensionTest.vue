<template>
  <div class="function-extension-test">
    <div class="test-header">
      <h1>功能扩展测试页面</h1>
      <p>测试字段筛选、主题切换、配置管理和后端接口对接功能</p>
    </div>

    <div class="test-sections">
      <!-- 字段筛选测试 -->
      <a-card title="字段筛选功能测试" class="test-card">
        <div class="test-controls">
          <a-space>
            <a-button type="primary" @click="testFieldFilter">测试字段筛选</a-button>
            <a-button @click="clearFieldFilter">清除筛选</a-button>
            <a-button @click="showFilterStats">显示筛选统计</a-button>
          </a-space>
        </div>

        <div class="test-results" v-if="filterTestResult">
          <h4>筛选结果:</h4>
          <pre>{{ filterTestResult }}</pre>
        </div>
      </a-card>

      <!-- 主题切换测试 -->
      <a-card title="主题切换功能测试" class="test-card">
        <div class="test-controls">
          <a-space>
            <a-button type="primary" @click="testThemeSwitch">切换主题</a-button>
            <a-button @click="testThemeVariables">测试主题变量</a-button>
            <a-button @click="resetTheme">重置主题</a-button>
          </a-space>
        </div>

        <div class="theme-preview">
          <div class="theme-sample light-theme">
            <h4>浅色主题预览</h4>
            <div class="sample-content">
              <div class="sample-node">示例节点</div>
              <div class="sample-edge">示例连线</div>
            </div>
          </div>
          <div class="theme-sample dark-theme">
            <h4>深色主题预览</h4>
            <div class="sample-content">
              <div class="sample-node">示例节点</div>
              <div class="sample-edge">示例连线</div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 配置管理测试 -->
      <a-card title="配置管理功能测试" class="test-card">
        <div class="test-controls">
          <a-space>
            <a-button type="primary" @click="testConfigSave">保存配置</a-button>
            <a-button @click="testConfigLoad">加载配置</a-button>
            <a-button @click="testConfigExport">导出配置</a-button>
            <a-button @click="testConfigImport">导入配置</a-button>
            <a-button @click="testConfigReset" danger>重置配置</a-button>
          </a-space>
        </div>

        <div class="config-display" v-if="currentConfig">
          <h4>当前配置:</h4>
          <pre>{{ JSON.stringify(currentConfig, null, 2) }}</pre>
        </div>
      </a-card>

      <!-- 后端接口测试 -->
      <a-card title="后端接口对接测试" class="test-card">
        <div class="test-controls">
          <a-space>
            <a-button type="primary" @click="testApiConnection">测试连接</a-button>
            <a-button @click="testSqlParsing">测试SQL解析</a-button>
            <a-button @click="testLineageQuery">测试血缘查询</a-button>
            <a-button @click="testConfigSync">测试配置同步</a-button>
          </a-space>
        </div>

        <div class="api-status">
          <a-descriptions title="API状态" bordered size="small">
            <a-descriptions-item label="连接状态">
              <a-tag :color="apiStatus.connected ? 'green' : 'red'">
                {{ apiStatus.connected ? '已连接' : '未连接' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最后请求时间">
              {{ apiStatus.lastRequestTime || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="响应时间">
              {{ apiStatus.responseTime ? `${apiStatus.responseTime}ms` : '无' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="api-results" v-if="apiTestResult">
          <h4>API测试结果:</h4>
          <pre>{{ apiTestResult }}</pre>
        </div>
      </a-card>

      <!-- 综合功能测试 -->
      <a-card title="综合功能测试" class="test-card">
        <div class="test-controls">
          <a-space>
            <a-button type="primary" @click="runAllTests">运行所有测试</a-button>
            <a-button @click="generateTestReport">生成测试报告</a-button>
            <a-button @click="clearAllResults">清除所有结果</a-button>
          </a-space>
        </div>

        <div class="test-summary" v-if="testSummary">
          <h4>测试摘要:</h4>
          <a-progress
            :percent="testSummary.successRate"
            :status="testSummary.successRate === 100 ? 'success' : 'active'"
          />
          <p>通过: {{ testSummary.passed }} / {{ testSummary.total }}</p>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { configManager, configUtils } from '@/utils/configManager'
import { defaultApiClient, mockApiClient, apiUtils } from '@/utils/apiClient'
import { useLineageStore } from '@/stores/lineageStore'

// 状态管理
const lineageStore = useLineageStore()

// 测试结果状态
const filterTestResult = ref<string>('')
const currentConfig = ref<any>(null)
const apiTestResult = ref<string>('')
const testSummary = ref<any>(null)

// API状态
const apiStatus = reactive({
  connected: false,
  lastRequestTime: '',
  responseTime: 0
})

// 字段筛选测试
const testFieldFilter = () => {
  console.log('🧪 测试字段筛选功能')

  // 模拟筛选配置
  const filterConfig = {
    dataTypes: ['string', 'number'],
    attributes: ['primary'],
    tables: ['users', 'orders'],
    fieldNamePattern: '.*id.*'
  }

  // 应用筛选
  lineageStore.applyFieldFilter(filterConfig)

  // 获取筛选结果
  const result = {
    config: filterConfig,
    filteredNodes: lineageStore.nodes.length,
    totalNodes: lineageStore.lineageData?.nodes.length || 0,
    timestamp: new Date().toISOString()
  }

  filterTestResult.value = JSON.stringify(result, null, 2)
  message.success('字段筛选测试完成')
}

const clearFieldFilter = () => {
  lineageStore.clearFieldFilter()
  filterTestResult.value = ''
  message.info('已清除字段筛选')
}

const showFilterStats = () => {
  const stats = {
    enabled: lineageStore.fieldFilterConfig.enabled,
    activeFilters: {
      dataTypes: lineageStore.fieldFilterConfig.dataTypes.length,
      attributes: lineageStore.fieldFilterConfig.attributes.length,
      tables: lineageStore.fieldFilterConfig.tables.length,
      pattern: !!lineageStore.fieldFilterConfig.fieldNamePattern
    },
    resultCount: lineageStore.nodes.length
  }

  filterTestResult.value = JSON.stringify(stats, null, 2)
}

// 主题切换测试
const testThemeSwitch = () => {
  console.log('🧪 测试主题切换功能')

  const currentTheme = lineageStore.theme.mode
  const newTheme = currentTheme === 'light' ? 'dark' : 'light'

  lineageStore.setTheme(newTheme)
  configUtils.applyTheme(newTheme)

  message.success(`主题已切换到${newTheme === 'light' ? '浅色' : '深色'}模式`)
}

const testThemeVariables = () => {
  const lightVars = configUtils.getThemeVariables('light')
  const darkVars = configUtils.getThemeVariables('dark')

  console.log('浅色主题变量:', lightVars)
  console.log('深色主题变量:', darkVars)

  message.info('主题变量已输出到控制台')
}

const resetTheme = () => {
  lineageStore.setTheme('light')
  configUtils.applyTheme('light')
  message.info('主题已重置为浅色模式')
}

// 配置管理测试
const testConfigSave = () => {
  console.log('🧪 测试配置保存功能')

  const config = {
    theme: 'dark' as const,
    layoutDirection: 'TB' as const,
    showFieldTypes: false,
    performanceMode: 'optimized' as const
  }

  configManager.updateConfig(config)
  configManager.saveToLocalStorage()
  currentConfig.value = configManager.getConfig()

  message.success('配置保存测试完成')
}

const testConfigLoad = () => {
  configManager.loadFromLocalStorage()
  currentConfig.value = configManager.getConfig()
  message.success('配置加载测试完成')
}

const testConfigExport = () => {
  configManager.exportConfig()
  message.success('配置导出测试完成')
}

const testConfigImport = () => {
  // 创建一个测试配置文件
  const testConfig = {
    theme: 'dark',
    layoutDirection: 'RL',
    performanceMode: 'extreme'
  }

  const blob = new Blob([JSON.stringify(testConfig, null, 2)], { type: 'application/json' })
  const file = new File([blob], 'test-config.json', { type: 'application/json' })

  configManager.importConfig(file).then(success => {
    if (success) {
      currentConfig.value = configManager.getConfig()
      message.success('配置导入测试完成')
    } else {
      message.error('配置导入测试失败')
    }
  })
}

const testConfigReset = () => {
  configManager.resetToDefault()
  currentConfig.value = configManager.getConfig()
  message.success('配置重置测试完成')
}

// 后端接口测试
const testApiConnection = async () => {
  console.log('🧪 测试API连接')

  const startTime = Date.now()
  const connected = await apiUtils.checkConnection()
  const endTime = Date.now()

  apiStatus.connected = connected
  apiStatus.lastRequestTime = new Date().toLocaleString()
  apiStatus.responseTime = endTime - startTime

  apiTestResult.value = JSON.stringify({
    connected,
    responseTime: apiStatus.responseTime,
    timestamp: apiStatus.lastRequestTime
  }, null, 2)

  message[connected ? 'success' : 'error'](`API连接${connected ? '成功' : '失败'}`)
}

const testSqlParsing = async () => {
  console.log('🧪 测试SQL解析接口')

  const request = {
    sql: 'SELECT u.id, u.name, o.amount FROM users u JOIN orders o ON u.id = o.user_id',
    databaseType: 'mysql' as any,
    options: {
      includeFieldLevel: true,
      includeCompleteLineage: true
    }
  }

  try {
    const response = await mockApiClient.parseSql(request)
    apiTestResult.value = JSON.stringify(response, null, 2)
    message.success('SQL解析测试完成')
  } catch (error: any) {
    apiTestResult.value = JSON.stringify({ error: error.message }, null, 2)
    message.error('SQL解析测试失败')
  }
}

const testLineageQuery = async () => {
  console.log('🧪 测试血缘查询接口')

  const request = {
    tableName: 'users',
    fieldName: 'id',
    direction: 'both' as const,
    maxDepth: 3
  }

  try {
    const response = await mockApiClient.queryLineage(request)
    apiTestResult.value = JSON.stringify(response, null, 2)
    message.success('血缘查询测试完成')
  } catch (error: any) {
    apiTestResult.value = JSON.stringify({ error: error.message }, null, 2)
    message.error('血缘查询测试失败')
  }
}

const testConfigSync = async () => {
  console.log('🧪 测试配置同步接口')

  const request = {
    configName: 'test-config',
    config: configManager.getConfig()
  }

  try {
    const response = await mockApiClient.saveConfig(request)
    apiTestResult.value = JSON.stringify(response, null, 2)
    message.success('配置同步测试完成')
  } catch (error: any) {
    apiTestResult.value = JSON.stringify({ error: error.message }, null, 2)
    message.error('配置同步测试失败')
  }
}

// 综合测试
const runAllTests = async () => {
  console.log('🧪 运行所有功能扩展测试')

  const tests = [
    { name: '字段筛选', fn: testFieldFilter },
    { name: '主题切换', fn: testThemeSwitch },
    { name: '配置保存', fn: testConfigSave },
    { name: 'API连接', fn: testApiConnection }
  ]

  let passed = 0
  const total = tests.length

  for (const test of tests) {
    try {
      await test.fn()
      passed++
      console.log(`✅ ${test.name} 测试通过`)
    } catch (error) {
      console.error(`❌ ${test.name} 测试失败:`, error)
    }
  }

  testSummary.value = {
    passed,
    total,
    successRate: Math.round((passed / total) * 100)
  }

  message.success(`所有测试完成，通过率: ${testSummary.value.successRate}%`)
}

const generateTestReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: testSummary.value,
    results: {
      fieldFilter: filterTestResult.value,
      config: currentConfig.value,
      api: apiTestResult.value
    },
    environment: {
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `test-report-${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)

  message.success('测试报告已生成并下载')
}

const clearAllResults = () => {
  filterTestResult.value = ''
  currentConfig.value = null
  apiTestResult.value = ''
  testSummary.value = null
  message.info('所有测试结果已清除')
}

// 初始化
onMounted(() => {
  console.log('🚀 功能扩展测试页面已加载')
  currentConfig.value = configManager.getConfig()
})
</script>

<style scoped>
.function-extension-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.test-sections {
  display: grid;
  gap: 24px;
}

.test-card {
  margin-bottom: 16px;
}

.test-controls {
  margin-bottom: 16px;
}

.test-results,
.config-display,
.api-results {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.test-results pre,
.config-display pre,
.api-results pre {
  margin: 0;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.theme-preview {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.theme-sample {
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.light-theme {
  background: #ffffff;
  color: #262626;
}

.dark-theme {
  background: #141414;
  color: #ffffff;
  border-color: #434343;
}

.sample-content {
  margin-top: 12px;
}

.sample-node,
.sample-edge {
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 4px;
  font-size: 12px;
}

.light-theme .sample-node {
  background: #fafafa;
  border: 1px solid #d9d9d9;
}

.light-theme .sample-edge {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.dark-theme .sample-node {
  background: #1f1f1f;
  border: 1px solid #434343;
}

.dark-theme .sample-edge {
  background: #003a8c;
  border: 1px solid #1890ff;
}

.api-status {
  margin: 16px 0;
}

.test-summary {
  margin-top: 16px;
}
</style>
