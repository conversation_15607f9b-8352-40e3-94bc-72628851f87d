/**
 * G6示例数据生成器
 * 基于G6 dagre-combo示例结构生成血缘数据
 */

import type { LineageData, TableInfo, LineageNode, LineageEdge } from '@/types/lineage'
import { generateUniqueId } from './graphDataTransform'

/**
 * 创建字段节点
 */
function createField(
  id: string,
  fieldName: string,
  tableName: string,
  dataType: string,
  options: {
    isPrimaryKey?: boolean
    isNullable?: boolean
    description?: string
    length?: number
    precision?: number
    scale?: number
  } = {}
): LineageNode {
  return {
    id,
    label: fieldName,
    tableName,
    fieldName,
    type: 'field',
    dataType: {
      type: dataType,
      length: options.length,
      precision: options.precision,
      scale: options.scale
    },
    description: options.description || `${fieldName}字段`,
    isKey: options.isPrimaryKey || false,
    isNullable: options.isNullable !== false
  }
}

/**
 * 创建边连接
 */
function createEdge(
  id: string,
  source: string,
  target: string,
  transformType: 'DIRECT' | 'TRANSFORM' | 'AGGREGATE' | 'JOIN' | 'CALCULATION',
  label: string,
  confidence: number = 1.0,
  expression?: string
): LineageEdge {
  return {
    id,
    source,
    target,
    label,
    transformType,
    confidence,
    expression,
    description: `${source} -> ${target} (${label})`
  }
}

/**
 * 生成G6 dagre-combo风格的示例数据
 * 模拟多层数据架构：源系统 -> 暂存层 -> 数据集市
 */
export function createG6SampleLineageData(): LineageData {
  // 定义表结构
  const tables: Record<string, TableInfo> = {
    // 源系统层
    'customer_source': {
      name: 'customer_source',
      type: 'table',
      description: '客户源数据表',
      schema: 'source',
      database: 'lineage_demo',
      combo: 'source_layer',
      fields: [
        createField('customer_source.id', 'id', 'customer_source', 'INT', { isPrimaryKey: true, description: '客户ID' }),
        createField('customer_source.name', 'name', 'customer_source', 'VARCHAR', { length: 100, description: '客户姓名' }),
        createField('customer_source.email', 'email', 'customer_source', 'VARCHAR', { length: 255, description: '邮箱' }),
        createField('customer_source.phone', 'phone', 'customer_source', 'VARCHAR', { length: 20, description: '电话' }),
        createField('customer_source.created_at', 'created_at', 'customer_source', 'TIMESTAMP', { description: '创建时间' })
      ],
      position: { x: 100, y: 100 }
    },
    'order_source': {
      name: 'order_source',
      type: 'table',
      description: '订单源数据表',
      schema: 'source',
      database: 'lineage_demo',
      combo: 'source_layer',
      fields: [
        createField('order_source.id', 'id', 'order_source', 'BIGINT', { isPrimaryKey: true, description: '订单ID' }),
        createField('order_source.customer_id', 'customer_id', 'order_source', 'INT', { description: '客户ID' }),
        createField('order_source.amount', 'amount', 'order_source', 'DECIMAL', { precision: 10, scale: 2, description: '订单金额' }),
        createField('order_source.status', 'status', 'order_source', 'VARCHAR', { length: 20, description: '订单状态' }),
        createField('order_source.order_date', 'order_date', 'order_source', 'DATE', { description: '订单日期' })
      ],
      position: { x: 100, y: 300 }
    },
    'product_source': {
      name: 'product_source',
      type: 'table',
      description: '产品源数据表',
      schema: 'source',
      database: 'lineage_demo',
      combo: 'source_layer',
      fields: [
        createField('product_source.id', 'id', 'product_source', 'INT', { isPrimaryKey: true, description: '产品ID' }),
        createField('product_source.name', 'name', 'product_source', 'VARCHAR', { length: 200, description: '产品名称' }),
        createField('product_source.category', 'category', 'product_source', 'VARCHAR', { length: 50, description: '产品类别' }),
        createField('product_source.price', 'price', 'product_source', 'DECIMAL', { precision: 8, scale: 2, description: '产品价格' })
      ],
      position: { x: 100, y: 500 }
    },

    // 暂存层
    'dim_customer': {
      name: 'dim_customer',
      type: 'table',
      description: '客户维度表',
      schema: 'staging',
      database: 'lineage_demo',
      combo: 'staging_layer',
      fields: [
        createField('dim_customer.customer_key', 'customer_key', 'dim_customer', 'INT', { isPrimaryKey: true, description: '客户代理键' }),
        createField('dim_customer.customer_id', 'customer_id', 'dim_customer', 'INT', { description: '客户业务键' }),
        createField('dim_customer.full_name', 'full_name', 'dim_customer', 'VARCHAR', { length: 100, description: '客户全名' }),
        createField('dim_customer.email_address', 'email_address', 'dim_customer', 'VARCHAR', { length: 255, description: '邮箱地址' }),
        createField('dim_customer.phone_number', 'phone_number', 'dim_customer', 'VARCHAR', { length: 20, description: '电话号码' }),
        createField('dim_customer.effective_date', 'effective_date', 'dim_customer', 'DATE', { description: '生效日期' })
      ],
      position: { x: 500, y: 100 }
    },
    'fact_order': {
      name: 'fact_order',
      type: 'table',
      description: '订单事实表',
      schema: 'staging',
      database: 'lineage_demo',
      combo: 'staging_layer',
      fields: [
        createField('fact_order.order_key', 'order_key', 'fact_order', 'BIGINT', { isPrimaryKey: true, description: '订单代理键' }),
        createField('fact_order.customer_key', 'customer_key', 'fact_order', 'INT', { description: '客户代理键' }),
        createField('fact_order.product_key', 'product_key', 'fact_order', 'INT', { description: '产品代理键' }),
        createField('fact_order.order_amount', 'order_amount', 'fact_order', 'DECIMAL', { precision: 12, scale: 2, description: '订单金额' }),
        createField('fact_order.order_status', 'order_status', 'fact_order', 'VARCHAR', { length: 20, description: '订单状态' }),
        createField('fact_order.order_date_key', 'order_date_key', 'fact_order', 'INT', { description: '订单日期键' })
      ],
      position: { x: 500, y: 300 }
    },

    // 数据集市层
    'customer_analytics': {
      name: 'customer_analytics',
      type: 'view',
      description: '客户分析视图',
      schema: 'mart',
      database: 'lineage_demo',
      combo: 'mart_layer',
      fields: [
        createField('customer_analytics.customer_id', 'customer_id', 'customer_analytics', 'INT', { description: '客户ID' }),
        createField('customer_analytics.customer_name', 'customer_name', 'customer_analytics', 'VARCHAR', { length: 100, description: '客户姓名' }),
        createField('customer_analytics.total_orders', 'total_orders', 'customer_analytics', 'BIGINT', { description: '总订单数' }),
        createField('customer_analytics.total_amount', 'total_amount', 'customer_analytics', 'DECIMAL', { precision: 15, scale: 2, description: '总消费金额' }),
        createField('customer_analytics.avg_order_value', 'avg_order_value', 'customer_analytics', 'DECIMAL', { precision: 10, scale: 2, description: '平均订单价值' }),
        createField('customer_analytics.first_order_date', 'first_order_date', 'customer_analytics', 'DATE', { description: '首次订单日期' }),
        createField('customer_analytics.last_order_date', 'last_order_date', 'customer_analytics', 'DATE', { description: '最近订单日期' })
      ],
      position: { x: 900, y: 200 }
    }
  }

  // 创建所有节点
  const nodes: LineageNode[] = []
  Object.values(tables).forEach(table => {
    nodes.push(...table.fields)
  })

  // 创建边连接 - 模拟完整的数据流
  const edges: LineageEdge[] = [
    // 源系统到暂存层的ETL过程
    createEdge('edge_1', 'customer_source.id', 'dim_customer.customer_id', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_2', 'customer_source.name', 'dim_customer.full_name', 'TRANSFORM', '数据清洗', 0.95, 'TRIM(UPPER(name))'),
    createEdge('edge_3', 'customer_source.email', 'dim_customer.email_address', 'TRANSFORM', '格式标准化', 0.9, 'LOWER(TRIM(email))'),
    createEdge('edge_4', 'customer_source.phone', 'dim_customer.phone_number', 'TRANSFORM', '格式化', 0.9, 'REGEXP_REPLACE(phone, "[^0-9]", "")'),
    createEdge('edge_5', 'customer_source.created_at', 'dim_customer.effective_date', 'TRANSFORM', '日期转换', 1.0, 'DATE(created_at)'),

    createEdge('edge_6', 'order_source.id', 'fact_order.order_key', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_7', 'order_source.customer_id', 'fact_order.customer_key', 'JOIN', '关联转换', 0.95),
    createEdge('edge_8', 'order_source.amount', 'fact_order.order_amount', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_9', 'order_source.status', 'fact_order.order_status', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_10', 'order_source.order_date', 'fact_order.order_date_key', 'TRANSFORM', '日期键转换', 1.0, 'TO_NUMBER(TO_CHAR(order_date, "YYYYMMDD"))'),

    // 暂存层到数据集市的聚合分析
    createEdge('edge_11', 'dim_customer.customer_id', 'customer_analytics.customer_id', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_12', 'dim_customer.full_name', 'customer_analytics.customer_name', 'DIRECT', '直接映射', 1.0),
    createEdge('edge_13', 'fact_order.order_key', 'customer_analytics.total_orders', 'AGGREGATE', '计数聚合', 1.0, 'COUNT(order_key)'),
    createEdge('edge_14', 'fact_order.order_amount', 'customer_analytics.total_amount', 'AGGREGATE', '求和聚合', 1.0, 'SUM(order_amount)'),
    createEdge('edge_15', 'fact_order.order_amount', 'customer_analytics.avg_order_value', 'AGGREGATE', '平均值聚合', 1.0, 'AVG(order_amount)'),
    createEdge('edge_16', 'fact_order.order_date_key', 'customer_analytics.first_order_date', 'AGGREGATE', '最小值聚合', 1.0, 'MIN(TO_DATE(order_date_key, "YYYYMMDD"))'),
    createEdge('edge_17', 'fact_order.order_date_key', 'customer_analytics.last_order_date', 'AGGREGATE', '最大值聚合', 1.0, 'MAX(TO_DATE(order_date_key, "YYYYMMDD"))')
  ]

  return {
    nodes,
    edges,
    tables,
    metadata: {
      sqlText: `
        -- 多层数据架构血缘示例
        -- 从源系统到数据集市的完整数据流转
        
        -- 客户维度表ETL
        INSERT INTO staging.dim_customer
        SELECT 
          ROW_NUMBER() OVER (ORDER BY id) as customer_key,
          id as customer_id,
          TRIM(UPPER(name)) as full_name,
          LOWER(TRIM(email)) as email_address,
          REGEXP_REPLACE(phone, '[^0-9]', '') as phone_number,
          DATE(created_at) as effective_date
        FROM source.customer_source;
        
        -- 订单事实表ETL
        INSERT INTO staging.fact_order
        SELECT 
          o.id as order_key,
          dc.customer_key,
          NULL as product_key,
          o.amount as order_amount,
          o.status as order_status,
          TO_NUMBER(TO_CHAR(o.order_date, 'YYYYMMDD')) as order_date_key
        FROM source.order_source o
        JOIN staging.dim_customer dc ON o.customer_id = dc.customer_id;
        
        -- 客户分析视图
        CREATE VIEW mart.customer_analytics AS
        SELECT 
          dc.customer_id,
          dc.full_name as customer_name,
          COUNT(fo.order_key) as total_orders,
          SUM(fo.order_amount) as total_amount,
          AVG(fo.order_amount) as avg_order_value,
          MIN(TO_DATE(fo.order_date_key, 'YYYYMMDD')) as first_order_date,
          MAX(TO_DATE(fo.order_date_key, 'YYYYMMDD')) as last_order_date
        FROM staging.dim_customer dc
        LEFT JOIN staging.fact_order fo ON dc.customer_key = fo.customer_key
        GROUP BY dc.customer_id, dc.full_name;
      `,
      parseTime: new Date().toISOString(),
      version: '2.0.0',
      description: 'G6 dagre-combo风格的多层数据架构血缘示例，展示从源系统到数据集市的完整ETL流程'
    }
  }
}
