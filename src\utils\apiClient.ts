/**
 * 后端API接口对接工具
 * 提供与后端服务的通信能力
 */

import type { LineageData, DatabaseType } from '@/types/lineage'

// API配置接口
export interface ApiConfig {
  baseUrl: string
  timeout: number
  headers?: Record<string, string>
  apiKey?: string
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
}

// SQL解析请求接口
export interface ParseSqlRequest {
  sql: string
  databaseType: DatabaseType
  options?: {
    includeFieldLevel?: boolean
    includeCompleteLineage?: boolean
    maxDepth?: number
  }
}

// 血缘查询请求接口
export interface LineageQueryRequest {
  tableName?: string
  fieldName?: string
  direction?: 'upstream' | 'downstream' | 'both'
  maxDepth?: number
  includeMetadata?: boolean
}

// 配置保存请求接口
export interface SaveConfigRequest {
  userId?: string
  configName: string
  config: {
    theme?: 'light' | 'dark'
    layoutDirection?: string
    showFieldTypes?: boolean
    showTableComments?: boolean
    performanceMode?: string
    fieldFilter?: any
  }
}

/**
 * API客户端类
 */
export class ApiClient {
  private config: ApiConfig
  private controller: AbortController | null = null

  constructor(config: ApiConfig) {
    this.config = {
      ...config,
      timeout: config.timeout || 30000
    }
  }

  /**
   * 更新API配置
   */
  updateConfig(config: Partial<ApiConfig>) {
    this.config = { ...this.config, ...config }
  }

  /**
   * 取消当前请求
   */
  cancelRequest() {
    if (this.controller) {
      this.controller.abort()
      this.controller = null
    }
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    this.controller = new AbortController()

    const url = `${this.config.baseUrl}${endpoint}`
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...this.config.headers
    }

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers: { ...headers, ...options.headers },
        signal: this.controller.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return {
        success: true,
        data,
        code: response.status
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          message: '请求已取消',
          code: 0
        }
      }

      return {
        success: false,
        message: error.message || '请求失败',
        code: error.status || 500
      }
    } finally {
      this.controller = null
    }
  }

  /**
   * 解析SQL获取血缘关系
   */
  async parseSql(request: ParseSqlRequest): Promise<ApiResponse<LineageData>> {
    return this.request<LineageData>('/api/lineage/parse-sql', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * 查询字段血缘关系
   */
  async queryLineage(request: LineageQueryRequest): Promise<ApiResponse<LineageData>> {
    const params = new URLSearchParams()
    Object.entries(request).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, String(value))
      }
    })

    return this.request<LineageData>(`/api/lineage/query?${params}`)
  }

  /**
   * 获取数据库表列表
   */
  async getTables(databaseType: DatabaseType): Promise<ApiResponse<string[]>> {
    return this.request<string[]>(`/api/metadata/tables?type=${databaseType}`)
  }

  /**
   * 获取表字段信息
   */
  async getTableFields(tableName: string): Promise<ApiResponse<any[]>> {
    return this.request<any[]>(`/api/metadata/fields?table=${tableName}`)
  }

  /**
   * 保存用户配置
   */
  async saveConfig(request: SaveConfigRequest): Promise<ApiResponse<void>> {
    return this.request<void>('/api/config/save', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * 加载用户配置
   */
  async loadConfig(userId?: string, configName?: string): Promise<ApiResponse<any>> {
    const params = new URLSearchParams()
    if (userId) params.append('userId', userId)
    if (configName) params.append('configName', configName)

    return this.request<any>(`/api/config/load?${params}`)
  }

  /**
   * 导出血缘图数据
   */
  async exportLineage(
    data: LineageData,
    format: 'json' | 'csv' | 'excel'
  ): Promise<ApiResponse<Blob>> {
    return this.request<Blob>('/api/lineage/export', {
      method: 'POST',
      body: JSON.stringify({ data, format })
    })
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request<{ status: string; timestamp: string }>('/api/health')
  }
}

/**
 * 默认API客户端实例
 */
export const defaultApiClient = new ApiClient({
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: 30000,
  headers: {
    'X-Client-Version': '1.0.0'
  }
})

/**
 * API工具函数
 */
export const apiUtils = {
  /**
   * 检查API连接状态
   */
  async checkConnection(client: ApiClient = defaultApiClient): Promise<boolean> {
    try {
      const response = await client.healthCheck()
      return response.success
    } catch {
      return false
    }
  },

  /**
   * 重试请求
   */
  async retryRequest<T>(
    requestFn: () => Promise<ApiResponse<T>>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<ApiResponse<T>> {
    let lastError: ApiResponse<T> | null = null

    for (let i = 0; i <= maxRetries; i++) {
      try {
        const result = await requestFn()
        if (result.success) {
          return result
        }
        lastError = result
      } catch (error: any) {
        lastError = {
          success: false,
          message: error.message || '请求失败'
        }
      }

      if (i < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }

    return lastError || {
      success: false,
      message: '请求失败'
    }
  },

  /**
   * 批量请求
   */
  async batchRequest<T>(
    requests: (() => Promise<ApiResponse<T>>)[],
    concurrency: number = 3
  ): Promise<ApiResponse<T>[]> {
    const results: ApiResponse<T>[] = []

    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency)
      const batchResults = await Promise.all(batch.map(req => req()))
      results.push(...batchResults)
    }

    return results
  }
}

/**
 * 模拟API响应（用于开发和测试）
 */
export const mockApiClient = {
  async parseSql(request: ParseSqlRequest): Promise<ApiResponse<LineageData>> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 返回模拟数据
    return {
      success: true,
      data: {
        nodes: [],
        edges: [],
        tables: {},
        metadata: {
          sqlText: request.sql,
          parseTime: new Date().toISOString(),
          version: '1.0.0'
        }
      }
    }
  },

  async queryLineage(request: LineageQueryRequest): Promise<ApiResponse<LineageData>> {
    await new Promise(resolve => setTimeout(resolve, 800))

    return {
      success: true,
      data: {
        nodes: [],
        edges: [],
        tables: {}
      }
    }
  },

  async saveConfig(request: SaveConfigRequest): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 500))

    return {
      success: true,
      message: '配置保存成功'
    }
  }
}
