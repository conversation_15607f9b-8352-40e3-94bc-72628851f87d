/**
 * 配置管理工具
 * 提供配置的保存、加载、导入、导出功能
 */

import { defaultApiClient, type SaveConfigRequest } from './apiClient'

// 配置项接口
export interface UserConfig {
  // 主题设置
  theme: 'light' | 'dark'
  
  // 布局设置
  layoutDirection: 'LR' | 'TB' | 'RL' | 'BT'
  showFieldTypes: boolean
  showTableComments: boolean
  showFieldDescriptions: boolean
  showDataTypes: boolean
  
  // 性能设置
  performanceMode: 'normal' | 'optimized' | 'extreme'
  enableVirtualRendering: boolean
  
  // 字段筛选设置
  fieldFilter: {
    enabled: boolean
    dataTypes: string[]
    attributes: string[]
    tables: string[]
    fieldNamePattern: string
  }
  
  // 图谱设置
  graphConfig: {
    width: number
    height: number
    fitView: boolean
    fitViewPadding: number
    animate: boolean
    animateCfg: {
      duration: number
      easing: string
    }
  }
  
  // 控制开关
  showFieldLevelLineage: boolean
  showCompleteLineage: boolean
  
  // 其他设置
  autoSave: boolean
  showMiniMap: boolean
  enableTooltips: boolean
}

// 默认配置
export const defaultConfig: UserConfig = {
  theme: 'light',
  layoutDirection: 'LR',
  showFieldTypes: true,
  showTableComments: true,
  showFieldDescriptions: true,
  showDataTypes: true,
  performanceMode: 'normal',
  enableVirtualRendering: false,
  fieldFilter: {
    enabled: false,
    dataTypes: [],
    attributes: [],
    tables: [],
    fieldNamePattern: ''
  },
  graphConfig: {
    width: 800,
    height: 600,
    fitView: true,
    fitViewPadding: 20,
    animate: true,
    animateCfg: {
      duration: 500,
      easing: 'easeInOutCubic'
    }
  },
  showFieldLevelLineage: true,
  showCompleteLineage: true,
  autoSave: true,
  showMiniMap: false,
  enableTooltips: true
}

/**
 * 配置管理器类
 */
export class ConfigManager {
  private static instance: ConfigManager
  private currentConfig: UserConfig
  private configKey = 'lineage-graph-config'
  private autoSaveTimer: number | null = null

  private constructor() {
    this.currentConfig = { ...defaultConfig }
    this.loadFromLocalStorage()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager()
    }
    return ConfigManager.instance
  }

  /**
   * 获取当前配置
   */
  getConfig(): UserConfig {
    return { ...this.currentConfig }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<UserConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...updates }
    
    if (this.currentConfig.autoSave) {
      this.scheduleAutoSave()
    }
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.currentConfig = { ...defaultConfig }
    this.saveToLocalStorage()
  }

  /**
   * 保存到本地存储
   */
  saveToLocalStorage(): void {
    try {
      localStorage.setItem(this.configKey, JSON.stringify(this.currentConfig))
    } catch (error) {
      console.warn('保存配置到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储加载
   */
  loadFromLocalStorage(): void {
    try {
      const saved = localStorage.getItem(this.configKey)
      if (saved) {
        const parsedConfig = JSON.parse(saved)
        this.currentConfig = { ...defaultConfig, ...parsedConfig }
      }
    } catch (error) {
      console.warn('从本地存储加载配置失败:', error)
      this.currentConfig = { ...defaultConfig }
    }
  }

  /**
   * 导出配置为JSON文件
   */
  exportConfig(): void {
    const configJson = JSON.stringify(this.currentConfig, null, 2)
    const blob = new Blob([configJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `lineage-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }

  /**
   * 从文件导入配置
   */
  async importConfig(file: File): Promise<boolean> {
    try {
      const text = await file.text()
      const importedConfig = JSON.parse(text)
      
      // 验证配置格式
      if (this.validateConfig(importedConfig)) {
        this.currentConfig = { ...defaultConfig, ...importedConfig }
        this.saveToLocalStorage()
        return true
      } else {
        throw new Error('配置文件格式无效')
      }
    } catch (error) {
      console.error('导入配置失败:', error)
      return false
    }
  }

  /**
   * 保存配置到服务器
   */
  async saveToServer(configName: string, userId?: string): Promise<boolean> {
    try {
      const request: SaveConfigRequest = {
        userId,
        configName,
        config: this.currentConfig
      }
      
      const response = await defaultApiClient.saveConfig(request)
      return response.success
    } catch (error) {
      console.error('保存配置到服务器失败:', error)
      return false
    }
  }

  /**
   * 从服务器加载配置
   */
  async loadFromServer(configName: string, userId?: string): Promise<boolean> {
    try {
      const response = await defaultApiClient.loadConfig(userId, configName)
      if (response.success && response.data) {
        this.currentConfig = { ...defaultConfig, ...response.data }
        this.saveToLocalStorage()
        return true
      }
      return false
    } catch (error) {
      console.error('从服务器加载配置失败:', error)
      return false
    }
  }

  /**
   * 获取配置预设
   */
  getPresets(): Record<string, Partial<UserConfig>> {
    return {
      'default': defaultConfig,
      'dark-theme': {
        theme: 'dark',
        performanceMode: 'optimized'
      },
      'performance': {
        performanceMode: 'extreme',
        enableVirtualRendering: true,
        graphConfig: {
          ...defaultConfig.graphConfig,
          animate: false
        }
      },
      'minimal': {
        showFieldTypes: false,
        showTableComments: false,
        showFieldDescriptions: false,
        showMiniMap: false,
        enableTooltips: false
      }
    }
  }

  /**
   * 应用预设配置
   */
  applyPreset(presetName: string): boolean {
    const presets = this.getPresets()
    const preset = presets[presetName]
    
    if (preset) {
      this.updateConfig(preset)
      return true
    }
    return false
  }

  /**
   * 验证配置格式
   */
  private validateConfig(config: any): boolean {
    if (!config || typeof config !== 'object') {
      return false
    }

    // 检查必要的字段
    const requiredFields = ['theme', 'layoutDirection', 'performanceMode']
    for (const field of requiredFields) {
      if (!(field in config)) {
        return false
      }
    }

    return true
  }

  /**
   * 计划自动保存
   */
  private scheduleAutoSave(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }
    
    this.autoSaveTimer = window.setTimeout(() => {
      this.saveToLocalStorage()
      this.autoSaveTimer = null
    }, 1000) // 1秒后自动保存
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
  }
}

/**
 * 配置管理器实例
 */
export const configManager = ConfigManager.getInstance()

/**
 * 配置工具函数
 */
export const configUtils = {
  /**
   * 获取主题相关的CSS变量
   */
  getThemeVariables(theme: 'light' | 'dark'): Record<string, string> {
    if (theme === 'dark') {
      return {
        '--color-background': '#141414',
        '--color-surface': '#1f1f1f',
        '--color-text': '#ffffff',
        '--color-text-secondary': 'rgba(255, 255, 255, 0.85)',
        '--color-border': 'rgba(255, 255, 255, 0.08)',
        '--color-shadow': 'rgba(0, 0, 0, 0.3)'
      }
    } else {
      return {
        '--color-background': '#ffffff',
        '--color-surface': '#fafafa',
        '--color-text': '#262626',
        '--color-text-secondary': '#595959',
        '--color-border': 'rgba(0, 0, 0, 0.06)',
        '--color-shadow': 'rgba(0, 0, 0, 0.08)'
      }
    }
  },

  /**
   * 应用主题到DOM
   */
  applyTheme(theme: 'light' | 'dark'): void {
    const variables = this.getThemeVariables(theme)
    const root = document.documentElement
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
    
    // 更新body类名
    document.body.className = document.body.className.replace(/theme-\w+/, '')
    document.body.classList.add(`theme-${theme}`)
  },

  /**
   * 检查配置兼容性
   */
  checkCompatibility(config: any): { compatible: boolean; issues: string[] } {
    const issues: string[] = []
    
    // 检查版本兼容性
    if (config.version && config.version !== '1.0.0') {
      issues.push(`配置版本 ${config.version} 可能不兼容当前版本`)
    }
    
    // 检查性能模式
    if (config.performanceMode && !['normal', 'optimized', 'extreme'].includes(config.performanceMode)) {
      issues.push(`未知的性能模式: ${config.performanceMode}`)
    }
    
    return {
      compatible: issues.length === 0,
      issues
    }
  }
}
