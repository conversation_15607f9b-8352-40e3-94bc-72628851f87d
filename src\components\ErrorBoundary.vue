<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <div v-if="!hasError" class="error-boundary-content">
      <slot />
    </div>

    <!-- 错误状态显示 -->
    <div v-else class="error-boundary-fallback">
      <div class="error-content">
        <div class="error-icon">
          <ExclamationCircleOutlined />
        </div>
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <!-- 错误详情（开发模式下显示） -->
        <div v-if="showDetails && errorDetails" class="error-details">
          <a-collapse>
            <a-collapse-panel key="1" header="错误详情">
              <pre class="error-stack">{{ errorDetails }}</pre>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <a-space>
            <a-button type="primary" @click="handleRetry">
              <ReloadOutlined />
              重试
            </a-button>
            <a-button @click="handleReportError" v-if="enableErrorReporting">
              <BugOutlined />
              报告错误
            </a-button>
            <a-button @click="handleGoHome">
              <HomeOutlined />
              返回首页
            </a-button>
          </a-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ExclamationCircleOutlined, 
  ReloadOutlined, 
  BugOutlined, 
  HomeOutlined 
} from '@ant-design/icons-vue'

// Props
interface Props {
  fallbackTitle?: string
  fallbackMessage?: string
  showDetails?: boolean
  enableErrorReporting?: boolean
  onError?: (error: Error, errorInfo: any) => void
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  fallbackTitle: '组件加载失败',
  fallbackMessage: '抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。',
  showDetails: import.meta.env.DEV,
  enableErrorReporting: true
})

// Emits
interface Emits {
  (e: 'error', error: Error, errorInfo: any): void
  (e: 'retry'): void
  (e: 'report', errorInfo: any): void
}

const emit = defineEmits<Emits>()

// 状态
const hasError = ref(false)
const errorTitle = ref('')
const errorMessage = ref('')
const errorDetails = ref('')
const errorInfo = ref<any>(null)

// 捕获组件错误
onErrorCaptured((error: Error, instance, errorInfo) => {
  console.error('ErrorBoundary caught an error:', error)
  console.error('Error info:', errorInfo)
  
  handleError(error, { instance, errorInfo })
  
  // 返回 false 阻止错误继续传播
  return false
})

// 处理错误
const handleError = (error: Error, info: any) => {
  hasError.value = true
  errorTitle.value = props.fallbackTitle
  errorMessage.value = error.message || props.fallbackMessage
  errorDetails.value = error.stack || ''
  errorInfo.value = { error, ...info }

  // 调用外部错误处理函数
  if (props.onError) {
    props.onError(error, info)
  }

  // 发出错误事件
  emit('error', error, info)

  // 记录错误到控制台
  console.group('🚨 ErrorBoundary Error Details')
  console.error('Error:', error)
  console.error('Error Info:', info)
  console.error('Stack:', error.stack)
  console.groupEnd()
}

// 重试处理
const handleRetry = () => {
  hasError.value = false
  errorTitle.value = ''
  errorMessage.value = ''
  errorDetails.value = ''
  errorInfo.value = null

  // 调用外部重试函数
  if (props.onRetry) {
    props.onRetry()
  }

  // 发出重试事件
  emit('retry')

  message.info('正在重新加载...')
}

// 报告错误
const handleReportError = () => {
  if (errorInfo.value) {
    emit('report', errorInfo.value)
    
    // 这里可以集成错误报告服务
    console.log('报告错误:', errorInfo.value)
    message.success('错误报告已发送')
  }
}

// 返回首页
const handleGoHome = () => {
  window.location.href = '/'
}

// 全局错误处理
const handleGlobalError = (event: ErrorEvent) => {
  console.error('Global error caught by ErrorBoundary:', event.error)
  handleError(event.error, { type: 'global', event })
}

const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
  console.error('Unhandled promise rejection caught by ErrorBoundary:', event.reason)
  const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason))
  handleError(error, { type: 'promise', event })
}

// 生命周期
onMounted(() => {
  // 监听全局错误
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
})

// 暴露方法
defineExpose({
  hasError,
  reset: handleRetry,
  reportError: handleReportError
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-boundary-content {
  width: 100%;
  height: 100%;
}

.error-boundary-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  background: #fafafa;
  border-radius: 8px;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  font-size: 64px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: #595959;
  margin-bottom: 32px;
  line-height: 1.6;
}

.error-details {
  margin-bottom: 32px;
  text-align: left;
}

.error-stack {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #595959;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.error-actions {
  margin-top: 24px;
}

/* 深色主题 */
.dark .error-boundary-fallback {
  background: #1f1f1f;
}

.dark .error-title {
  color: #f0f0f0;
}

.dark .error-message {
  color: #bfbfbf;
}

.dark .error-stack {
  background: #262626;
  border-color: #434343;
  color: #bfbfbf;
}
</style>
