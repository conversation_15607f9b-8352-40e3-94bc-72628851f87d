import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../tests/FieldEdgeTest.vue'),
    },
    {
      path: '/interaction-test',
      name: 'interaction-test',
      component: () => import('../tests/InteractionTest.vue'),
    },
    {
      path: '/field-interaction-test',
      name: 'field-interaction-test',
      component: () => import('../tests/FieldInteractionTest.vue'),
    },
    {
      path: '/advanced-interaction-test',
      name: 'AdvancedInteractionTest',
      component: () => import('@/tests/AdvancedInteractionTest.vue')
    },
    {
      path: '/test/layout-optimization',
      name: 'LayoutOptimizationTest',
      component: () => import('@/tests/LayoutOptimizationTest.vue')
    },
    {
      path: '/data-transform-test',
      name: 'DataTransformTest',
      component: () => import('@/tests/DataTransformTest.vue')
    },
    {
      path: '/style-test',
      name: 'StyleOptimizationTest',
      component: () => import('@/tests/StyleOptimizationTest.vue')
    },
    {
      path: '/performance-test',
      name: 'PerformanceOptimizationTest',
      component: () => import('@/tests/PerformanceOptimizationTest.vue')
    },
    {
      path: '/function-extension-test',
      name: 'FunctionExtensionTest',
      component: () => import('@/tests/FunctionExtensionTest.vue')
    },
    {
      path: '/error-handling-test',
      name: 'ErrorHandlingTest',
      component: () => import('@/tests/ErrorHandlingTest.vue')
    },
    {
      path: '/demo',
      name: 'demo',
      component: () => import('@/views/DemoView.vue')
    },
    {
      path: '/integration-test',
      name: 'integration-test',
      component: () => import('@/tests/IntegrationTest.vue')
    },
    {
      path: '/detailed-performance-test',
      name: 'detailed-performance-test',
      component: () => import('@/tests/DetailedPerformanceTest.vue')
    },
    {
      path: '/compatibility-test',
      name: 'compatibility-test',
      component: () => import('@/tests/CompatibilityTest.vue')
    },
    {
      path: '/ux-test',
      name: 'ux-test',
      component: () => import('@/tests/UXTest.vue')
    },
    {
      path: '/code-review',
      name: 'code-review',
      component: () => import('@/tests/CodeReview.vue')
    },
    {
      path: '/g6-basic-test',
      name: 'G6BasicTest',
      component: () => import('@/tests/G6BasicTest.vue')
    },
    {
      path: '/g6-sample-test',
      name: 'G6SampleTest',
      component: () => import('@/views/G6SampleTest.vue')
    },
    {
      path: '/centering-test',
      name: 'CenteringTest',
      component: () => import('@/tests/CenteringTest.vue')
    },
  ],
})

export default router
