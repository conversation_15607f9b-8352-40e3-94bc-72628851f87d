# 字段级别数据血缘图组件

一个基于 Vue 3 + AntV G6 的现代化数据血缘图可视化组件，专为商业数据治理平台设计。

## ✨ 特性

- 🎯 **字段级别血缘** - 精确到字段级别的数据血缘关系追踪
- 🎨 **现代化设计** - 扁平化设计风格，支持深色/浅色主题
- 🚀 **高性能渲染** - 支持大数据量场景，虚拟渲染和懒加载
- 🔧 **高度可配置** - 丰富的配置选项和自定义能力
- 📱 **响应式布局** - 适配不同屏幕尺寸
- 🛠️ **TypeScript** - 完整的类型定义和类型安全
- 🧪 **完整测试** - 全面的功能测试和示例演示

## 🏗️ 技术架构

- **前端框架**: Vue 3 + Composition API + TypeScript
- **构建工具**: Vite
- **图形库**: AntV G6 v5
- **UI组件**: Ant Design Vue
- **状态管理**: Pinia
- **布局算法**: Dagre
- **代码编辑器**: CodeMirror 6

## 📦 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 访问 http://localhost:5173
```

### 构建生产版本

```bash
# 类型检查
npm run type-check

# 构建项目
npm run build

# 预览构建结果
npm run preview
```

## 🎮 在线演示

访问以下页面体验不同功能：

- **主页面**: `/` - 完整的血缘图功能
- **演示页面**: `/demo` - 各种业务场景演示
- **测试页面**:
  - `/test` - 基础功能测试
  - `/interaction-test` - 交互功能测试
  - `/field-interaction-test` - 字段交互测试
  - `/advanced-interaction-test` - 高级交互测试
  - `/data-transform-test` - 数据转换测试
  - `/test/layout-optimization` - 布局优化测试
  - `/style-test` - 样式优化测试
  - `/performance-test` - 性能优化测试
  - `/function-extension-test` - 功能扩展测试
  - `/error-handling-test` - 错误处理测试

## 📚 文档

- [组件使用文档](./docs/components.md) - 详细的组件API和使用方法
- [API接口文档](./docs/api.md) - 完整的接口定义和数据格式
- [示例数据和演示](./docs/examples.md) - 丰富的示例数据和业务场景
- [开发指南](./docs/development-guide.md) - 开发环境搭建和最佳实践

## 🚀 核心功能

### 1. 字段级别血缘关系

- 精确的字段到字段连接
- 支持多种转换类型（直接映射、别名、计算、聚合等）
- 智能路径追踪和高亮显示

### 2. 丰富的交互功能

- 鼠标悬浮字段高亮
- 字段点击详情展示
- 搜索定位功能
- 图谱缩放和拖拽
- MiniMap导航

### 3. 高级功能

- SQL解析和血缘生成
- 多种布局算法支持
- 图谱导出（PNG/PDF）
- 配置管理和主题切换
- 性能优化模式

### 4. 数据处理

- 多格式数据转换
- 数据验证和清理
- 错误处理和恢复
- 统计分析功能

## 🎯 使用示例

### 基础用法

```vue
<template>
  <LineageLayout
    :initial-sql="sqlText"
    :show-sql-editor="true"
    :show-config-panel="true"
  />
</template>

<script setup>
import { LineageLayout } from '@/components'

const sqlText = `
  SELECT u.id, u.name, o.total_amount
  FROM users u
  JOIN orders o ON u.id = o.user_id
`
</script>
```

### 高级配置

```typescript
import { useLineageStore } from '@/stores/lineageStore'
import { transformToG6Data } from '@/utils/graphDataTransform'

const store = useLineageStore()

// 设置血缘数据
store.setLineageData(lineageData)

// 配置图谱选项
store.setGraphConfig({
  layoutDirection: 'LR',
  showFieldTypes: true,
  performanceMode: 'optimized'
})
```

## 🏢 业务场景

### 电商场景
- 用户、订单、产品等核心业务表
- 复杂的多表关联关系
- 销售分析和用户行为追踪

### 金融场景
- 客户、账户、交易等金融表
- 风险评估和合规追踪
- 资金流向分析

### 数据仓库
- ETL过程血缘追踪
- 数据质量监控
- 影响分析和变更管理

## 🔧 配置选项

```typescript
interface UserConfig {
  theme: 'light' | 'dark'
  layoutDirection: 'LR' | 'TB' | 'RL' | 'BT'
  showFieldTypes: boolean
  performanceMode: 'normal' | 'optimized' | 'extreme'
  enableVirtualRendering: boolean
  fieldFilter: FieldFilterConfig
  graphConfig: GraphConfig
}
```

## 🧪 测试

项目包含完整的功能测试页面，覆盖所有核心功能：

- 基础功能测试
- 交互功能测试
- 性能优化测试
- 错误处理测试
- 样式和主题测试

## 📈 性能特性

- **虚拟渲染**: 大数据量场景下只渲染可见元素
- **懒加载**: 分批加载数据，优先显示重要节点
- **智能缓存**: 缓存计算结果，避免重复计算
- **节流优化**: 高频操作使用节流，保证流畅性

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [AntV G6](https://g6.antv.vision/) - 图可视化引擎
- [Ant Design Vue](https://antdv.com/) - 企业级UI组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
