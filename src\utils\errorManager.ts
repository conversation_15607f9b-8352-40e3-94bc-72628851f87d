/**
 * 全局错误管理器
 * 提供统一的错误处理、提示和报告功能
 */

import { message, notification } from 'ant-design-vue'
import { h } from 'vue'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  BUSINESS = 'business',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

// 错误级别枚举
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface ErrorInfo {
  id: string
  type: ErrorType
  level: ErrorLevel
  title: string
  message: string
  details?: any
  timestamp: string
  stack?: string
  context?: Record<string, any>
  retryable?: boolean
  reportable?: boolean
}

// 错误处理选项
export interface ErrorHandleOptions {
  showMessage?: boolean
  showNotification?: boolean
  autoReport?: boolean
  retryable?: boolean
  duration?: number
  context?: Record<string, any>
}

// 重试配置
export interface RetryConfig {
  maxRetries: number
  delay: number
  backoff?: boolean
}

/**
 * 错误管理器类
 */
export class ErrorManager {
  private errors: Map<string, ErrorInfo> = new Map()
  private retryCallbacks: Map<string, () => Promise<void>> = new Map()
  private errorReportCallback?: (error: ErrorInfo) => void

  /**
   * 设置错误报告回调
   */
  setErrorReportCallback(callback: (error: ErrorInfo) => void) {
    this.errorReportCallback = callback
  }

  /**
   * 处理错误
   */
  handleError(
    error: Error | string | any,
    type: ErrorType = ErrorType.UNKNOWN,
    options: ErrorHandleOptions = {}
  ): string {
    const errorInfo = this.createErrorInfo(error, type, options)
    this.errors.set(errorInfo.id, errorInfo)

    // 显示用户提示
    if (options.showMessage !== false) {
      this.showErrorMessage(errorInfo)
    }

    if (options.showNotification) {
      this.showErrorNotification(errorInfo, options)
    }

    // 自动报告错误
    if (options.autoReport && this.errorReportCallback) {
      this.errorReportCallback(errorInfo)
    }

    // 记录到控制台
    this.logError(errorInfo)

    return errorInfo.id
  }

  /**
   * 创建错误信息
   */
  private createErrorInfo(
    error: Error | string | any,
    type: ErrorType,
    options: ErrorHandleOptions
  ): ErrorInfo {
    const id = this.generateErrorId()
    const timestamp = new Date().toISOString()

    let title: string
    let message: string
    let details: any
    let stack: string | undefined

    if (error instanceof Error) {
      title = this.getErrorTitle(type)
      message = error.message || '发生了未知错误'
      details = error
      stack = error.stack
    } else if (typeof error === 'string') {
      title = this.getErrorTitle(type)
      message = error
    } else if (error && typeof error === 'object') {
      title = error.title || this.getErrorTitle(type)
      message = error.message || '发生了未知错误'
      details = error
    } else {
      title = this.getErrorTitle(type)
      message = '发生了未知错误'
      details = error
    }

    return {
      id,
      type,
      level: this.getErrorLevel(type),
      title,
      message,
      details,
      timestamp,
      stack,
      context: options.context,
      retryable: options.retryable,
      reportable: type !== ErrorType.VALIDATION
    }
  }

  /**
   * 获取错误标题
   */
  private getErrorTitle(type: ErrorType): string {
    const titleMap: Record<ErrorType, string> = {
      [ErrorType.NETWORK]: '网络错误',
      [ErrorType.VALIDATION]: '数据验证错误',
      [ErrorType.PERMISSION]: '权限错误',
      [ErrorType.BUSINESS]: '业务错误',
      [ErrorType.SYSTEM]: '系统错误',
      [ErrorType.UNKNOWN]: '未知错误'
    }
    return titleMap[type]
  }

  /**
   * 获取错误级别
   */
  private getErrorLevel(type: ErrorType): ErrorLevel {
    const levelMap: Record<ErrorType, ErrorLevel> = {
      [ErrorType.NETWORK]: ErrorLevel.WARNING,
      [ErrorType.VALIDATION]: ErrorLevel.INFO,
      [ErrorType.PERMISSION]: ErrorLevel.ERROR,
      [ErrorType.BUSINESS]: ErrorLevel.WARNING,
      [ErrorType.SYSTEM]: ErrorLevel.CRITICAL,
      [ErrorType.UNKNOWN]: ErrorLevel.ERROR
    }
    return levelMap[type]
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(errorInfo: ErrorInfo) {
    const messageText = `${errorInfo.title}: ${errorInfo.message}`

    switch (errorInfo.level) {
      case ErrorLevel.INFO:
        message.info(messageText)
        break
      case ErrorLevel.WARNING:
        message.warning(messageText)
        break
      case ErrorLevel.ERROR:
      case ErrorLevel.CRITICAL:
        message.error(messageText)
        break
    }
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(errorInfo: ErrorInfo, options: ErrorHandleOptions) {
    const notificationConfig: any = {
      message: errorInfo.title,
      description: errorInfo.message,
      duration: options.duration || (errorInfo.level === ErrorLevel.CRITICAL ? 0 : 4.5)
    }

    // 添加重试按钮
    if (errorInfo.retryable && this.retryCallbacks.has(errorInfo.id)) {
      notificationConfig.btn = h('a-button', {
        type: 'primary',
        size: 'small',
        onClick: () => this.retryError(errorInfo.id)
      }, '重试')
    }

    switch (errorInfo.level) {
      case ErrorLevel.INFO:
        notification.info(notificationConfig)
        break
      case ErrorLevel.WARNING:
        notification.warning(notificationConfig)
        break
      case ErrorLevel.ERROR:
      case ErrorLevel.CRITICAL:
        notification.error(notificationConfig)
        break
    }
  }

  /**
   * 记录错误到控制台
   */
  private logError(errorInfo: ErrorInfo) {
    const logMethod = errorInfo.level === ErrorLevel.CRITICAL ? 'error' :
                     errorInfo.level === ErrorLevel.ERROR ? 'error' :
                     errorInfo.level === ErrorLevel.WARNING ? 'warn' : 'info'

    console.group(`🚨 ${errorInfo.title} [${errorInfo.id}]`)
    console[logMethod]('Message:', errorInfo.message)
    console[logMethod]('Type:', errorInfo.type)
    console[logMethod]('Level:', errorInfo.level)
    console[logMethod]('Timestamp:', errorInfo.timestamp)

    if (errorInfo.context) {
      console[logMethod]('Context:', errorInfo.context)
    }

    if (errorInfo.details) {
      console[logMethod]('Details:', errorInfo.details)
    }

    if (errorInfo.stack) {
      console[logMethod]('Stack:', errorInfo.stack)
    }

    console.groupEnd()
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 注册重试回调
   */
  registerRetry(errorId: string, callback: () => Promise<void>) {
    this.retryCallbacks.set(errorId, callback)
  }

  /**
   * 重试错误
   */
  async retryError(errorId: string) {
    const callback = this.retryCallbacks.get(errorId)
    if (callback) {
      try {
        await callback()
        this.clearError(errorId)
        message.success('重试成功')
      } catch (error) {
        this.handleError(error, ErrorType.SYSTEM, {
          showMessage: true,
          context: { retryErrorId: errorId }
        })
      }
    }
  }

  /**
   * 清除错误
   */
  clearError(errorId: string) {
    this.errors.delete(errorId)
    this.retryCallbacks.delete(errorId)
  }

  /**
   * 获取错误信息
   */
  getError(errorId: string): ErrorInfo | undefined {
    return this.errors.get(errorId)
  }

  /**
   * 获取所有错误
   */
  getAllErrors(): ErrorInfo[] {
    return Array.from(this.errors.values())
  }

  /**
   * 清除所有错误
   */
  clearAllErrors() {
    this.errors.clear()
    this.retryCallbacks.clear()
  }

  /**
   * 带重试的异步操作
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = { maxRetries: 3, delay: 1000 }
  ): Promise<T> {
    let lastError: any

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error

        if (attempt < config.maxRetries) {
          const delay = config.backoff ?
            config.delay * Math.pow(2, attempt) :
            config.delay

          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError
  }
}

// 全局错误管理器实例
export const errorManager = new ErrorManager()

// 便捷方法
export const handleError = (
  error: Error | string | any,
  type: ErrorType = ErrorType.UNKNOWN,
  options: ErrorHandleOptions = {}
) => errorManager.handleError(error, type, options)

export const handleNetworkError = (error: any, options: ErrorHandleOptions = {}) =>
  errorManager.handleError(error, ErrorType.NETWORK, { retryable: true, ...options })

export const handleValidationError = (error: any, options: ErrorHandleOptions = {}) =>
  errorManager.handleError(error, ErrorType.VALIDATION, { showNotification: false, ...options })

export const handleBusinessError = (error: any, options: ErrorHandleOptions = {}) =>
  errorManager.handleError(error, ErrorType.BUSINESS, options)

export const handleSystemError = (error: any, options: ErrorHandleOptions = {}) =>
  errorManager.handleError(error, ErrorType.SYSTEM, {
    showNotification: true,
    autoReport: true,
    ...options
  })
