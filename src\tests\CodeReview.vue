<template>
  <div class="code-review">
    <div class="test-header">
      <h1>🔍 最终代码审查</h1>
      <p>全面分析代码质量、性能和可维护性</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-space>
        <a-button
          type="primary"
          size="large"
          :loading="isRunningAnalysis"
          @click="runCodeAnalysis"
        >
          <template #icon><SearchOutlined /></template>
          运行代码分析
        </a-button>

        <a-button @click="runQuickScan">快速扫描</a-button>
        <a-button @click="exportReport">导出报告</a-button>
        <a-button @click="clearResults">清空结果</a-button>
      </a-space>
    </div>

    <!-- 分析结果展示 -->
    <div v-if="analysisResult" class="analysis-results">
      <!-- 总体评分 -->
      <a-card title="代码质量总评" class="score-card" :bordered="false">
        <div class="score-display">
          <a-progress
            type="circle"
            :percent="analysisResult.overall"
            :status="getScoreStatus(analysisResult.overall)"
            :width="120"
            :stroke-color="getScoreColor(analysisResult.overall)"
          />
          <div class="score-info">
            <h2>{{ analysisResult.overall }}/100</h2>
            <p>{{ getQualityLabel(analysisResult.overall) }}</p>
          </div>
          <div class="metrics-summary">
            <a-statistic title="总文件数" :value="analysisResult.summary.totalFiles" />
            <a-statistic title="代码行数" :value="analysisResult.summary.linesOfCode" />
            <a-statistic title="严重问题" :value="analysisResult.summary.criticalIssues" />
          </div>
        </div>
      </a-card>

      <!-- 各维度评分 -->
      <a-card title="质量维度分析" :bordered="false" class="dimensions-card">
        <a-row :gutter="16">
          <a-col :span="4" v-for="(score, dimension) in analysisResult.dimensions" :key="dimension">
            <div class="dimension-item">
              <a-progress
                type="circle"
                :percent="score"
                :width="80"
                :status="getScoreStatus(score)"
                :stroke-color="getScoreColor(score)"
              />
              <h4>{{ getDimensionLabel(dimension) }}</h4>
              <p>{{ score }}/100</p>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 代码指标 -->
      <a-card title="代码指标" :bordered="false" class="metrics-card">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-statistic
              title="复杂度"
              :value="analysisResult.metrics.complexity"
              :precision="1"
              :value-style="{ color: analysisResult.metrics.complexity < 10 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
          <a-col :span="4">
            <a-statistic
              title="重复率"
              :value="analysisResult.metrics.duplication"
              suffix="%"
              :value-style="{ color: analysisResult.metrics.duplication < 10 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
          <a-col :span="4">
            <a-statistic
              title="测试覆盖率"
              :value="analysisResult.metrics.coverage"
              suffix="%"
              :value-style="{ color: analysisResult.metrics.coverage > 80 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
          <a-col :span="4">
            <a-statistic
              title="依赖数量"
              :value="analysisResult.metrics.dependencies"
              :value-style="{ color: analysisResult.metrics.dependencies < 50 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
          <a-col :span="4">
            <a-statistic
              title="包大小"
              :value="analysisResult.metrics.bundleSize"
              suffix="MB"
              :precision="1"
              :value-style="{ color: analysisResult.metrics.bundleSize < 3 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
          <a-col :span="4">
            <a-statistic
              title="加载时间"
              :value="analysisResult.metrics.loadTime"
              suffix="ms"
              :value-style="{ color: analysisResult.metrics.loadTime < 1500 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 优势和劣势 -->
      <a-row :gutter="16" class="strengths-weaknesses">
        <a-col :span="12">
          <a-card title="项目优势" :bordered="false">
            <div v-if="analysisResult.summary.strengths.length === 0" class="empty-state">
              <a-empty description="暂无明显优势" />
            </div>
            <div v-else>
              <a-tag
                v-for="strength in analysisResult.summary.strengths"
                :key="strength"
                color="green"
                class="strength-tag"
              >
                <CheckCircleOutlined /> {{ strength }}
              </a-tag>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="改进空间" :bordered="false">
            <div v-if="analysisResult.summary.weaknesses.length === 0" class="empty-state">
              <a-empty description="无明显问题" />
            </div>
            <div v-else>
              <a-tag
                v-for="weakness in analysisResult.summary.weaknesses"
                :key="weakness"
                color="orange"
                class="weakness-tag"
              >
                <ExclamationCircleOutlined /> {{ weakness }}
              </a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 问题列表 -->
      <a-card title="发现的问题" :bordered="false" class="issues-card">
        <div v-if="analysisResult.issues.length === 0" class="empty-state">
          <a-result status="success" title="未发现问题" sub-title="代码质量良好" />
        </div>
        <div v-else>
          <a-table
            :columns="issueColumns"
            :data-source="analysisResult.issues"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'severity'">
                <a-tag :color="getSeverityColor(record.severity)">
                  {{ getSeverityLabel(record.severity) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'category'">
                <a-tag color="blue">
                  {{ getDimensionLabel(record.category) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button type="link" size="small" @click="showIssueDetails(record)">
                  查看详情
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </a-card>

      <!-- 改进建议 -->
      <a-card title="改进建议" :bordered="false" class="recommendations-card">
        <div v-if="analysisResult.recommendations.length === 0" class="empty-state">
          <a-empty description="暂无改进建议" />
        </div>
        <div v-else>
          <a-list :data-source="sortedRecommendations" size="large">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span class="recommendation-title">
                      <a-tag :color="getPriorityColor(item.priority)" size="small">
                        {{ getPriorityLabel(item.priority) }}优先级
                      </a-tag>
                      {{ item.title }}
                    </span>
                  </template>
                  <template #description>
                    <div class="recommendation-content">
                      <p>{{ item.description }}</p>
                      <div class="recommendation-benefits">
                        <strong>预期收益：</strong>
                        <a-tag v-for="benefit in item.benefits" :key="benefit" size="small">
                          {{ benefit }}
                        </a-tag>
                      </div>
                      <div class="recommendation-implementation">
                        <strong>实施方案：</strong>
                        <span>{{ item.implementation }}</span>
                      </div>
                      <div class="recommendation-effort">
                        <strong>工作量：</strong>
                        <a-tag :color="getEffortColor(item.effort)" size="small">
                          {{ getEffortLabel(item.effort) }}
                        </a-tag>
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-card>

      <!-- 最佳实践建议 -->
      <a-card title="最佳实践建议" :bordered="false" class="best-practices-card">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="practice-item">
              <h4><CodeOutlined /> 代码规范</h4>
              <ul>
                <li>使用ESLint和Prettier保持代码风格一致</li>
                <li>遵循Vue 3 Composition API最佳实践</li>
                <li>使用TypeScript严格模式</li>
              </ul>
            </div>
          </a-col>

          <a-col :span="8">
            <div class="practice-item">
              <h4><RocketOutlined /> 性能优化</h4>
              <ul>
                <li>实现组件懒加载和代码分割</li>
                <li>优化图片和静态资源</li>
                <li>使用Web Workers处理复杂计算</li>
              </ul>
            </div>
          </a-col>

          <a-col :span="8">
            <div class="practice-item">
              <h4><SafetyOutlined /> 安全防护</h4>
              <ul>
                <li>定期更新依赖包</li>
                <li>实施输入验证和sanitization</li>
                <li>使用HTTPS和CSP策略</li>
              </ul>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无分析结果，请运行代码分析" />
    </div>

    <!-- 问题详情模态框 -->
    <a-modal
      v-model:open="issueDetailVisible"
      title="问题详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedIssue" class="issue-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="问题类型">
            <a-tag :color="getSeverityColor(selectedIssue.severity)">
              {{ getSeverityLabel(selectedIssue.severity) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="所属维度">
            <a-tag color="blue">
              {{ getDimensionLabel(selectedIssue.category) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="问题描述">{{ selectedIssue.description }}</a-descriptions-item>
          <a-descriptions-item label="文件位置" v-if="selectedIssue.file">{{ selectedIssue.file }}</a-descriptions-item>
          <a-descriptions-item label="行号" v-if="selectedIssue.line">{{ selectedIssue.line }}</a-descriptions-item>
          <a-descriptions-item label="修复建议">{{ selectedIssue.suggestion }}</a-descriptions-item>
          <a-descriptions-item label="影响说明">{{ selectedIssue.impact }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CodeOutlined,
  RocketOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'
import {
  codeQualityAnalyzer,
  type CodeQualityAnalysisResult,
  type CodeQualityIssue,
  type CodeQualityRecommendation
} from '@/utils/codeQualityAnalyzer'

// 响应式数据
const isRunningAnalysis = ref(false)
const analysisResult = ref<CodeQualityAnalysisResult | null>(null)
const issueDetailVisible = ref(false)
const selectedIssue = ref<CodeQualityIssue | null>(null)

// 表格列定义
const issueColumns = [
  { title: '严重程度', key: 'severity', width: 100 },
  { title: '类别', key: 'category', width: 120 },
  { title: '问题标题', dataIndex: 'title', key: 'title' },
  { title: '文件', dataIndex: 'file', key: 'file', width: 200 },
  { title: '操作', key: 'actions', width: 100 }
]

// 计算属性
const sortedRecommendations = computed(() => {
  if (!analysisResult.value) return []

  const priorityOrder = { high: 3, medium: 2, low: 1 }
  return [...analysisResult.value.recommendations].sort((a, b) =>
    priorityOrder[b.priority] - priorityOrder[a.priority]
  )
})

// 运行代码分析
const runCodeAnalysis = async () => {
  isRunningAnalysis.value = true

  try {
    message.info('开始代码质量分析...')

    const result = await codeQualityAnalyzer.analyzeCodeQuality()
    analysisResult.value = result

    if (result.overall >= 80) {
      message.success(`代码分析完成！总分: ${result.overall}/100`)
    } else if (result.overall >= 60) {
      message.warning(`代码分析完成，总分: ${result.overall}/100，建议优化`)
    } else {
      message.error(`代码分析完成，总分: ${result.overall}/100，需要重点改进`)
    }
  } catch (error) {
    console.error('代码分析失败:', error)
    message.error('代码分析失败')
  } finally {
    isRunningAnalysis.value = false
  }
}

// 快速扫描
const runQuickScan = async () => {
  message.info('运行快速代码扫描...')

  // 模拟快速扫描结果
  const quickResult: CodeQualityAnalysisResult = {
    overall: 82,
    dimensions: {
      structure: 85,
      performance: 78,
      maintainability: 80,
      security: 88,
      testability: 75,
      documentation: 70
    },
    issues: [
      {
        category: 'performance',
        severity: 'medium',
        title: '包体积较大',
        description: '当前包体积为2.5MB，建议优化',
        file: 'dist/bundle.js',
        suggestion: '使用代码分割和Tree Shaking',
        impact: '提升首屏加载速度'
      },
      {
        category: 'maintainability',
        severity: 'low',
        title: '存在代码重复',
        description: '多个组件中存在相似的样式定义',
        suggestion: '提取公共样式',
        impact: '减少代码重复，提升维护效率'
      }
    ],
    recommendations: [
      {
        category: 'performance',
        priority: 'high',
        title: '性能优化',
        description: '优化包大小和加载性能',
        benefits: ['提升用户体验', '减少加载时间'],
        effort: 'medium',
        implementation: '1. 代码分割 2. 懒加载 3. 资源优化'
      }
    ],
    metrics: {
      complexity: 6.5,
      duplication: 8.2,
      coverage: 75,
      dependencies: 45,
      bundleSize: 2.5,
      loadTime: 1200
    },
    summary: {
      strengths: ['良好的代码结构', '高安全性'],
      weaknesses: ['性能有待提升'],
      criticalIssues: 0,
      totalFiles: 25,
      linesOfCode: 3500
    }
  }

  analysisResult.value = quickResult
  message.success('快速扫描完成！')
}

// 获取评分状态
const getScoreStatus = (score: number): 'success' | 'active' | 'exception' => {
  if (score >= 80) return 'success'
  if (score >= 60) return 'active'
  return 'exception'
}

// 获取评分颜色
const getScoreColor = (score: number): string => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

// 获取质量标签
const getQualityLabel = (score: number): string => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '需改进'
  return '较差'
}

// 获取维度标签
const getDimensionLabel = (dimension: string): string => {
  const labels: { [key: string]: string } = {
    structure: '代码结构',
    performance: '性能优化',
    maintainability: '可维护性',
    security: '安全性',
    testability: '可测试性',
    documentation: '文档完整性'
  }
  return labels[dimension] || dimension
}

// 获取严重程度颜色
const getSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'critical': return '#ff4d4f'
    case 'high': return '#fa8c16'
    case 'medium': return '#faad14'
    case 'low': return '#52c41a'
    default: return '#d9d9d9'
  }
}

// 获取严重程度标签
const getSeverityLabel = (severity: string): string => {
  switch (severity) {
    case 'critical': return '严重'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取优先级颜色
const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'high': return '#ff4d4f'
    case 'medium': return '#faad14'
    case 'low': return '#52c41a'
    default: return '#d9d9d9'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority: string): string => {
  switch (priority) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取工作量颜色
const getEffortColor = (effort: string): string => {
  switch (effort) {
    case 'high': return '#ff4d4f'
    case 'medium': return '#faad14'
    case 'low': return '#52c41a'
    default: return '#d9d9d9'
  }
}

// 获取工作量标签
const getEffortLabel = (effort: string): string => {
  switch (effort) {
    case 'high': return '大'
    case 'medium': return '中'
    case 'low': return '小'
    default: return '未知'
  }
}

// 显示问题详情
const showIssueDetails = (issue: CodeQualityIssue) => {
  selectedIssue.value = issue
  issueDetailVisible.value = true
}

// 导出报告
const exportReport = () => {
  if (!analysisResult.value) {
    message.warning('请先运行代码分析')
    return
  }

  const report = {
    timestamp: new Date().toISOString(),
    analysis: analysisResult.value,
    metadata: {
      projectName: 'Lineage Web',
      version: '1.0.0',
      analyzer: 'Code Quality Analyzer v1.0',
      environment: {
        nodeVersion: 'unknown',
        platform: navigator.platform,
        userAgent: navigator.userAgent
      }
    }
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `code-quality-report-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('代码质量报告已导出')
}

// 清空结果
const clearResults = () => {
  analysisResult.value = null
  message.info('分析结果已清空')
}
</script>

<style scoped>
.code-review {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
}

.test-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-controls {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.score-card :deep(.ant-card-head-title) {
  color: white;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 24px;
}

.score-info h2 {
  margin: 0;
  color: white;
  font-size: 32px;
}

.score-info p {
  margin: 8px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.metrics-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-left: auto;
}

.metrics-summary .ant-statistic {
  color: white;
}

.metrics-summary :deep(.ant-statistic-title) {
  color: rgba(255, 255, 255, 0.8);
}

.metrics-summary :deep(.ant-statistic-content-value) {
  color: white;
}

.dimensions-card {
  margin-bottom: 24px;
}

.dimension-item {
  text-align: center;
  padding: 16px;
}

.dimension-item h4 {
  margin: 12px 0 4px 0;
  color: #333;
  font-size: 14px;
}

.dimension-item p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.metrics-card {
  margin-bottom: 24px;
}

.strengths-weaknesses {
  margin-bottom: 24px;
}

.strength-tag {
  margin: 4px;
  padding: 4px 8px;
}

.weakness-tag {
  margin: 4px;
  padding: 4px 8px;
}

.issues-card {
  margin-bottom: 24px;
}

.recommendations-card {
  margin-bottom: 24px;
}

.recommendation-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendation-content {
  margin-top: 8px;
}

.recommendation-benefits {
  margin: 8px 0;
}

.recommendation-implementation {
  margin: 8px 0;
  color: #666;
}

.recommendation-effort {
  margin: 8px 0;
}

.best-practices-card {
  margin-bottom: 24px;
}

.practice-item {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
  height: 100%;
}

.practice-item h4 {
  margin: 0 0 12px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.practice-item ul {
  margin: 0;
  padding-left: 16px;
}

.practice-item li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.issue-detail {
  padding: 16px;
}

.empty-state {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .code-review {
    padding: 16px;
  }

  .test-header {
    padding: 16px;
  }

  .test-header h1 {
    font-size: 24px;
  }

  .score-display {
    flex-direction: column;
    text-align: center;
  }

  .metrics-summary {
    margin-left: 0;
    flex-direction: row;
    justify-content: space-around;
  }

  .dimensions-card .ant-col {
    margin-bottom: 16px;
  }

  .strengths-weaknesses .ant-col {
    margin-bottom: 16px;
  }

  .best-practices-card .ant-col {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 进度条样式 */
.ant-progress-circle .ant-progress-text {
  font-weight: 600;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 统计数字样式 */
.ant-statistic-content-value {
  font-weight: 600;
}

/* 表格样式优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 16px;
}

/* 列表样式 */
.ant-list-item-meta-title {
  margin-bottom: 4px;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 500;
}
</style>
