<template>
  <div id="lineage-graph" class="lineage-graph">
    <!-- G6图谱将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { Graph } from '@antv/g6'

// 页面加载时直接执行核心渲染逻辑
onMounted(() => {
  fetch('https://assets.antv.antgroup.com/g6/dagre-combo.json')
    .then((res) => res.json())
    .then((data) => {
      const graph = new Graph({
        container: 'lineage-graph',
        autoFit: 'center',
        data,
        node: {
          type: 'rect',
          style: {
            size: [60, 30],
            radius: 8,
            labelText: (d) => d.id,
            labelBackground: true,
            ports: [{ placement: 'top' }, { placement: 'bottom' }],
          },
          palette: {
            field: (d) => d.combo,
          },
        },
        edge: {
          type: 'cubic-vertical',
          style: {
            endArrow: true,
          },
        },
        combo: {
          type: 'rect',
          style: {
            radius: 8,
            labelText: (d) => d.id,
          },
        },
        layout: {
          type: 'antv-dagre',
          ranksep: 50,
          nodesep: 5,
          sortByCombo: true,
        },
        behaviors: ['drag-element', 'drag-canvas', 'zoom-canvas'],
      });

      graph.render();
    });
});
</script>

<style scoped>
.lineage-graph {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.02);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .lineage-graph {
    background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  }
}
</style>
