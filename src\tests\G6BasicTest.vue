<template>
  <div class="g6-basic-test">
    <h1>G6 基础功能测试</h1>

    <div class="test-controls">
      <a-button type="primary" @click="initBasicGraph">初始化基础图谱</a-button>
      <a-button @click="addNode">添加节点</a-button>
      <a-button @click="clearGraph">清空图谱</a-button>
    </div>

    <div class="test-info">
      <p>状态: {{ status }}</p>
      <p>错误: {{ error }}</p>
    </div>

    <div ref="containerRef" class="graph-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Graph } from '@antv/g6'

const containerRef = ref<HTMLDivElement>()
const graphInstance = ref<Graph>()
const status = ref('未初始化')
const error = ref('')

// 初始化基础图谱
const initBasicGraph = () => {
  if (!containerRef.value) {
    error.value = '容器未找到'
    return
  }

  try {
    status.value = '正在初始化...'
    error.value = ''

    // 创建最简单的G6配置
    const config = {
      container: containerRef.value,
      width: 800,
      height: 600,
      // 使用最基础的配置
      node: {
        type: 'circle',
        style: {
          fill: '#1890ff',
          stroke: '#1890ff',
          lineWidth: 2,
          r: 20
        }
      },
      edge: {
        type: 'line',
        style: {
          stroke: '#999',
          lineWidth: 1
        }
      },
      // 基础数据
      data: {
        nodes: [
          { id: 'node1', data: { x: 100, y: 100 } },
          { id: 'node2', data: { x: 300, y: 100 } },
          { id: 'node3', data: { x: 200, y: 200 } }
        ],
        edges: [
          { id: 'edge1', source: 'node1', target: 'node2' },
          { id: 'edge2', source: 'node2', target: 'node3' }
        ]
      }
    }

    // 销毁之前的实例
    if (graphInstance.value) {
      graphInstance.value.destroy()
    }

    // 创建新实例
    graphInstance.value = new Graph(config)

    // 渲染
    graphInstance.value.render()

    status.value = '初始化成功'
    console.log('G6 graph initialized successfully')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '未知错误'
    status.value = '初始化失败'
    console.error('Failed to initialize G6 graph:', err)
  }
}

// 添加节点
const addNode = () => {
  if (!graphInstance.value) {
    error.value = '图谱未初始化'
    return
  }

  try {
    const nodeId = `node${Date.now()}`
    // G6 v5 API: 使用addData添加单个节点数据
    graphInstance.value.addData({
      nodes: [{
        id: nodeId,
        data: {
          x: Math.random() * 600 + 100,
          y: Math.random() * 400 + 100
        }
      }],
      edges: []
    })

    status.value = `添加节点 ${nodeId} 成功`
  } catch (err) {
    error.value = err instanceof Error ? err.message : '添加节点失败'
    console.error('Failed to add node:', err)
  }
}

// 清空图谱
const clearGraph = () => {
  if (!graphInstance.value) {
    error.value = '图谱未初始化'
    return
  }

  try {
    graphInstance.value.setData({ nodes: [], edges: [] })
    graphInstance.value.render()
    status.value = '图谱已清空'
  } catch (err) {
    error.value = err instanceof Error ? err.message : '清空失败'
    console.error('Failed to clear graph:', err)
  }
}

onMounted(() => {
  // 自动初始化
  setTimeout(() => {
    initBasicGraph()
  }, 100)
})

onUnmounted(() => {
  if (graphInstance.value) {
    graphInstance.value.destroy()
  }
})
</script>

<style scoped>
.g6-basic-test {
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .ant-btn {
  margin-right: 10px;
}

.test-info {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.graph-container {
  width: 800px;
  height: 600px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
</style>
