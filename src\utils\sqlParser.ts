/**
 * SQL解析工具（基础版本）
 * 用于解析SQL语句并提取血缘关系
 */

import type { LineageData, LineageNode, LineageEdge, TableInfo } from '@/types/lineage'
import { generateUniqueId, generateFieldId, transformRawDataToLineageData } from './graphDataTransform'
import { createG6SampleLineageData } from './g6SampleData'

/**
 * SQL格式化选项
 */
export interface SqlFormatOptions {
  indent: string;
  uppercase: boolean;
  linesBetweenQueries: number;
}

/**
 * 默认格式化选项
 */
const DEFAULT_FORMAT_OPTIONS: SqlFormatOptions = {
  indent: '  ',
  uppercase: true,
  linesBetweenQueries: 2
}

/**
 * 格式化SQL语句
 * @param sql 原始SQL
 * @param options 格式化选项
 * @returns 格式化后的SQL
 */
export function formatSql(sql: string, options: Partial<SqlFormatOptions> = {}): string {
  const opts = { ...DEFAULT_FORMAT_OPTIONS, ...options }

  // 简单的SQL格式化实现
  let formatted = sql
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim()

  // 关键字大写
  if (opts.uppercase) {
    const keywords = [
      'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN',
      'FULL JOIN', 'ON', 'GROUP BY', 'ORDER BY', 'HAVING', 'UNION', 'UNION ALL',
      'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'TABLE', 'VIEW',
      'INDEX', 'DATABASE', 'SCHEMA', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS',
      'BETWEEN', 'LIKE', 'IS', 'NULL', 'DISTINCT', 'COUNT', 'SUM', 'AVG', 'MAX', 'MIN'
    ]

    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
      formatted = formatted.replace(regex, keyword)
    })
  }

  // 添加换行和缩进
  formatted = formatted
    .replace(/\bSELECT\b/gi, '\nSELECT')
    .replace(/\bFROM\b/gi, '\nFROM')
    .replace(/\bWHERE\b/gi, '\nWHERE')
    .replace(/\bJOIN\b/gi, '\nJOIN')
    .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN')
    .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
    .replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN')
    .replace(/\bFULL JOIN\b/gi, '\nFULL JOIN')
    .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
    .replace(/\bORDER BY\b/gi, '\nORDER BY')
    .replace(/\bHAVING\b/gi, '\nHAVING')
    .replace(/\bUNION\b/gi, '\nUNION')

  // 添加缩进
  const lines = formatted.split('\n')
  const indentedLines = lines.map((line, index) => {
    if (index === 0) return line.trim()
    return opts.indent + line.trim()
  })

  return indentedLines.join('\n').trim()
}

/**
 * 解析SQL中的表名（简单实现）
 * @param sql SQL语句
 * @returns 表名数组
 */
export function extractTableNames(sql: string): string[] {
  const tables: string[] = []

  // 简单的正则表达式匹配表名
  const fromRegex = /FROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/gi
  const joinRegex = /JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/gi

  let match

  // 提取FROM子句中的表名
  while ((match = fromRegex.exec(sql)) !== null) {
    const tableName = match[1].trim()
    if (!tables.includes(tableName)) {
      tables.push(tableName)
    }
  }

  // 提取JOIN子句中的表名
  while ((match = joinRegex.exec(sql)) !== null) {
    const tableName = match[1].trim()
    if (!tables.includes(tableName)) {
      tables.push(tableName)
    }
  }

  return tables
}

/**
 * 解析SQL中的字段名（简单实现）
 * @param sql SQL语句
 * @returns 字段名数组
 */
export function extractFieldNames(sql: string): string[] {
  const fields: string[] = []

  // 提取SELECT子句中的字段
  const selectRegex = /SELECT\s+(.*?)\s+FROM/gis
  const match = selectRegex.exec(sql)

  if (match) {
    const selectClause = match[1]

    // 分割字段（简单处理，不考虑复杂的嵌套情况）
    const fieldParts = selectClause.split(',')

    fieldParts.forEach(part => {
      const trimmed = part.trim()

      // 跳过聚合函数和复杂表达式
      if (trimmed === '*' || trimmed.includes('(')) {
        return
      }

      // 提取字段名（可能包含表名前缀）
      const fieldMatch = trimmed.match(/([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)/)
      if (fieldMatch) {
        fields.push(fieldMatch[1])
      }
    })
  }

  return fields
}

/**
 * 创建示例血缘数据（用于演示）
 * @param sql SQL语句
 * @returns 血缘数据
 */
export function createSampleLineageData(sql?: string): LineageData {
  // 使用新的G6风格示例数据
  const g6Data = createG6SampleLineageData()

  // 如果提供了SQL，更新metadata中的sqlText
  if (sql) {
    g6Data.metadata = {
      ...g6Data.metadata,
      sqlText: sql
    }
  }

  return g6Data
}

/**
 * 创建传统示例血缘数据（保留原有功能，简化版本）
 * @param sql SQL语句
 * @returns 血缘数据
 */
export function createLegacySampleLineageData(sql?: string): LineageData {
  // 简化的传统示例数据，用于向后兼容
  const tables: { [key: string]: TableInfo } = {
    'users': {
      name: 'users',
      type: 'table',
      description: '用户表',
      fields: [
        {
          id: 'users.id',
          label: 'id',
          tableName: 'users',
          fieldName: 'id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '用户ID',
          isKey: true,
          isNullable: false
        },
        {
          id: 'users.name',
          label: 'name',
          tableName: 'users',
          fieldName: 'name',
          type: 'field',
          dataType: { type: 'VARCHAR', length: 100 },
          description: '用户姓名',
          isNullable: false
        }
      ],
      position: { x: 100, y: 100 }
    },
    'orders': {
      name: 'orders',
      type: 'table',
      description: '订单表',
      fields: [
        {
          id: 'orders.id',
          label: 'id',
          tableName: 'orders',
          fieldName: 'id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '订单ID',
          isKey: true,
          isNullable: false
        },
        {
          id: 'orders.user_id',
          label: 'user_id',
          tableName: 'orders',
          fieldName: 'user_id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '用户ID',
          isNullable: false
        }
      ],
      position: { x: 400, y: 100 }
    }
  }

  const nodes: LineageNode[] = []
  Object.values(tables).forEach(table => {
    nodes.push(...table.fields)
  })

  const edges: LineageEdge[] = [
    {
      id: generateUniqueId('edge'),
      source: 'users.id',
      target: 'orders.user_id',
      label: 'Join',
      transformType: 'JOIN',
      confidence: 1.0
    }
  ]

  return {
    nodes,
    edges,
    tables,
    metadata: {
      sqlText: sql || 'SELECT u.id, u.name, o.id as order_id FROM users u JOIN orders o ON u.id = o.user_id',
      parseTime: new Date().toISOString(),
      version: '1.0.0'
    }
  }
}

/**
 * 解析SQL语句并生成血缘数据（增强版本）
 * @param sql SQL语句
 * @returns 血缘数据
 */
export function parseSqlToLineageData(sql: string): LineageData {
  try {
    // 提取表名和字段名
    const tableNames = extractTableNames(sql)
    const fieldNames = extractFieldNames(sql)

    if (tableNames.length === 0) {
      console.warn('未能从SQL中提取到表名，使用示例数据')
      return createSampleLineageData(sql)
    }

    // 创建基础的血缘数据结构
    const tables: { [key: string]: TableInfo } = {}
    const nodes: LineageNode[] = []
    const edges: LineageEdge[] = []

    // 为每个表创建基础结构
    tableNames.forEach((tableName, index) => {
      const tableFields = generateFieldsForTable(tableName, fieldNames)

      tables[tableName] = {
        name: tableName,
        type: 'table',
        fields: tableFields,
        description: `从SQL解析的表: ${tableName}`,
        position: {
          x: index * 300,
          y: 0
        }
      }

      nodes.push(...tableFields)
    })

    // 生成简单的血缘关系（基于字段名匹配）
    if (tableNames.length > 1) {
      const sourceTable = tableNames[0]
      const targetTable = tableNames[tableNames.length - 1]

      const sourceFields = tables[sourceTable].fields
      const targetFields = tables[targetTable].fields

      // 基于字段名相似性创建连接
      sourceFields.forEach(sourceField => {
        targetFields.forEach(targetField => {
          if (isFieldsRelated(sourceField.fieldName, targetField.fieldName)) {
            edges.push({
              id: generateUniqueId('edge'),
              source: sourceField.id,
              target: targetField.id,
              label: 'Mapped',
              transformType: 'DIRECT',
              confidence: 0.8
            })
          }
        })
      })
    }

    return {
      tables,
      nodes,
      edges,
      metadata: {
        sqlText: sql,
        parseTime: new Date().toISOString(),
        version: '1.0.0'
      }
    }
  } catch (error) {
    console.error('SQL解析失败:', error)
    return createSampleLineageData(sql)
  }
}

/**
 * 为表生成字段
 * @param tableName 表名
 * @param extractedFields 从SQL提取的字段名
 * @returns 字段列表
 */
function generateFieldsForTable(tableName: string, extractedFields: string[]): LineageNode[] {
  const fields: LineageNode[] = []

  // 添加常见的主键字段
  fields.push({
    id: `${tableName}.id`,
    label: 'id',
    tableName,
    fieldName: 'id',
    type: 'field',
    dataType: { type: 'INT' },
    description: '主键',
    isKey: true,
    isNullable: false
  })

  // 添加从SQL提取的字段
  extractedFields.forEach(fieldName => {
    if (fieldName.includes('.')) {
      const [tablePrefix, actualFieldName] = fieldName.split('.')
      if (tablePrefix === tableName || tablePrefix === tableName.charAt(0)) {
        fields.push({
          id: `${tableName}.${actualFieldName}`,
          label: actualFieldName,
          tableName,
          fieldName: actualFieldName,
          type: 'field',
          dataType: { type: 'VARCHAR', length: 255 },
          description: `从SQL解析的字段: ${actualFieldName}`,
          isNullable: true
        })
      }
    }
  })

  // 如果没有提取到字段，添加一些默认字段
  if (fields.length === 1) {
    const defaultFields = ['name', 'created_at', 'updated_at']
    defaultFields.forEach(fieldName => {
      fields.push({
        id: `${tableName}.${fieldName}`,
        label: fieldName,
        tableName,
        fieldName,
        type: 'field',
        dataType: fieldName.includes('_at')
          ? { type: 'TIMESTAMP' }
          : { type: 'VARCHAR', length: 255 },
        description: `默认字段: ${fieldName}`,
        isNullable: true
      })
    })
  }

  return fields
}

/**
 * 判断两个字段是否相关
 * @param field1 字段1
 * @param field2 字段2
 * @returns 是否相关
 */
function isFieldsRelated(field1: string, field2: string): boolean {
  // 简单的字段关联判断逻辑
  const normalized1 = field1.toLowerCase().replace(/[_-]/g, '')
  const normalized2 = field2.toLowerCase().replace(/[_-]/g, '')

  // 完全匹配
  if (normalized1 === normalized2) return true

  // 包含关系
  if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) return true

  // ID字段特殊处理
  if ((field1.toLowerCase().includes('id') && field2.toLowerCase().includes('id')) ||
      (field1.toLowerCase() === 'id' || field2.toLowerCase() === 'id')) {
    return true
  }

  return false
}
